package main

import (
	"context"
	"os"
	"os/signal"
	"qing-profiles/config"
	"qing-profiles/controllers"
	"qing-profiles/database"
	"qing-profiles/middleware"
	"syscall"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/middleware/logger"
	"github.com/kataras/iris/v12/middleware/recover"
	"github.com/sirupsen/logrus"
)

func main() {
	// 获取配置
	cfg := config.GetConfig()

	// 设置日志
	setupLogging(cfg)

	// 初始化数据库
	if err := database.InitDatabases(); err != nil {
		logrus.WithError(err).Fatal("初始化数据库失败")
	}
	defer database.CloseDatabases()

	// 创建iris app
	app := iris.New()

	// 配置Iris
	setupIris(app)

	// 设置中间件
	setupMiddleware(app)

	// 设置路由
	setupRoutes(app)

	// 启动服务器
	startServer(app, cfg)
}

// 设置日志
func setupLogging(cfg *config.Config) {
	// 根据环境设置日志级别
	if cfg.IsProduction() {
		logrus.SetLevel(logrus.InfoLevel)
		logrus.SetFormatter(&logrus.JSONFormatter{})
	} else {
		logrus.SetLevel(logrus.DebugLevel)
		logrus.SetFormatter(&logrus.TextFormatter{
			FullTimestamp: true,
		})
	}

	logrus.WithField("env", cfg.Server.Env).Info("日志配置完成")
}

// 设置Iris
func setupIris(app *iris.Application) {
	// 配置Iris设置
	app.Configure(iris.WithConfiguration(iris.Configuration{
		DisableStartupLog:                 false,
		DisableInterruptHandler:           true,
		DisablePathCorrection:             false,
		EnablePathEscape:                  false,
		FireMethodNotAllowed:              true,
		DisableBodyConsumptionOnUnmarshal: false,
		TimeFormat:                        "2006-01-02 15:04:05",
		Charset:                           "UTF-8",
	}))
}

// 设置中间件
func setupMiddleware(app *iris.Application) {
	// 恢复中间件
	app.Use(recover.New())

	// 请求日志中间件
	app.Use(logger.New())

	// 自定义请求日志中间件
	app.Use(middleware.RequestLogger())
}

// 设置路由
func setupRoutes(app *iris.Application) {
	// 初始化控制器
	healthController := controllers.NewHealthController()
	profileController := controllers.NewProfileController()
	profileConfigController := controllers.NewProfileConfigController()
	appConfigController := controllers.NewAppConfigController()
	mediaController := controllers.NewMediaController()

	// 公共路由（无需头部验证）
	app.Get("/ping", healthController.Ping)

	// API路由（统一处理，中间件根据路径判断是否需要认证）
	api := app.Party("/api")
	api.Use(middleware.CheckRequestSignature()) // 请求签名中间件
	api.Use(middleware.CheckAuthorization())    // 必需的授权中间件
	api.Use(middleware.RequestHeaders())        // 请求头处理中间件
	{
		// 公共API端点（无需认证）
		api.Get("/app/config", appConfigController.GetAppConfig) // 获取应用配置

		// 用户资料相关端点
		api.Post("/users/profile/pwd-initialize", profileController.InitializePassword) // 初始化密码
		api.Post("/users/profile/initialize", profileController.InitializeProfile)      // 初始化用户资料
		api.Get("/users/profile/details", profileController.GetUserProfileDetails)      // 获取用户全部信息
		api.Get("/users/profile/basic", profileController.GetUserProfileBasicInfo)      // 获取用户基本信息

		// 用户资料配置相关端点
		api.Get("/users/profile/qingid/update-quota", profileController.GetUserQingIdUpdateQuota)   // 获取 qing id 修改剩余次数
		api.Get("/users/profile/username/update-quota", profileController.GetUserQingIdUpdateQuota) // 获取 username 修改剩余次数

		api.Get("/users/profile/config/birthday", profileConfigController.GetBirthdayConfig)                      // 获取生日配置
		api.Get("/users/profile/config/zodiac-sign", profileConfigController.GetZodiacSignConfig)                 // 获取星座配置
		api.Get("/users/profile/config/height", profileConfigController.GetHeightConfig)                          // 获取身高配置
		api.Get("/users/profile/config/weight", profileConfigController.GetWeightConfig)                          // 获取体重配置
		api.Get("/users/profile/config/regions", profileConfigController.GetRegionsConfig)                        // 获取地区配置
		api.Get("/users/profile/config/roles", profileController.GetRoles)                                        // 获取角色配置
		api.Get("/users/profile/config/current-city", profileController.GetRoles)                                 // 获取现居地配置
		api.Get("/users/profile/config/hometown", profileController.GetRoles)                                     // 获取家乡配置
		api.Get("/users/profile/config/mbti", profileConfigController.GetMBTIConfig)                              // 获取MBTI配置
		api.Get("/users/profile/config/personalities", profileConfigController.GetPersonalitiesConfig)            // 获取个性标签配置
		api.Get("/users/profile/config/interests", profileConfigController.GetInterestsConfig)                    // 获取兴趣配置
		api.Get("/users/profile/config/relationship-status", profileConfigController.GetRelationshipStatusConfig) // 获取情感状态配置
		api.Get("/users/profile/config/future-goals", profileConfigController.GetFutureGoalsConfig)               // 获取未来预期配置

		// 修改用户基本资料相关端点
		api.Post("/users/profile/username", profileController.UpdateUsername)         // 修改用户名
		api.Post("/users/profile/avatar", profileController.UpdateUserAvatar)         // 修改用户头像
		api.Post("/users/profile/qingid", profileController.UpdateUserQingId)         // 修改用户qing id
		api.Post("/users/profile/bio", profileController.UpdateUserBio)               // 修改用户签名
		api.Post("/users/profile/birthday", profileController.UpdateUserBirthday)     // 修改用户生日
		api.Post("/users/profile/zodiac-sign", profileController.UpdateUserX)         // 修改-星座
		api.Post("/users/profile/height", profileController.UpdateUserX)              // 修改-身高
		api.Post("/users/profile/weight", profileController.UpdateUserX)              // 修改-体重
		api.Post("/users/profile/role", profileController.UpdateUserX)                // 修改-角色
		api.Post("/users/profile/regular-city", profileController.UpdateUserX)        // 修改-常居地
		api.Post("/users/profile/hometown", profileController.UpdateUserX)            // 修改-家乡
		api.Post("/users/profile/mbti", profileController.UpdateUserX)                // 修改-MBTI
		api.Post("/users/profile/personalities", profileController.UpdateUserX)       // 修改-个性标签
		api.Post("/users/profile/interests", profileController.UpdateUserX)           // 修改-兴趣
		api.Post("/users/profile/relationship-status", profileController.UpdateUserX) // 修改-我的状态
		api.Post("/users/profile/future-goals", profileController.UpdateUserX)        // 修改-未来预期

		// 媒体相关端点
		api.Get("/users/media/{scenario:string}", mediaController.GetUploadMediaURL) // 获取上传媒体的URL
		api.Post("/users/media/{scenario:string}", mediaController.MediaUploaded)    // 处理媒体上传完成
	}
}

// 启动服务器
func startServer(app *iris.Application, cfg *config.Config) {
	// 服务器地址
	addr := ":" + cfg.Server.Port

	// 创建一个通道来监听中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// 在goroutine中启动服务器
	go func() {
		logrus.WithFields(logrus.Fields{
			"port": cfg.Server.Port,
			"env":  cfg.Server.Env,
		}).Info("启动HTTP服务器")

		if err := app.Listen(addr); err != nil {
			logrus.WithError(err).Error("服务器启动失败")
		}
	}()

	// 等待中断信号
	<-quit
	logrus.Info("正在关闭服务器...")

	// 创建带超时的上下文用于优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 关闭服务器
	if err := app.Shutdown(ctx); err != nil {
		logrus.WithError(err).Error("服务器强制关闭")
	} else {
		logrus.Info("服务器优雅停止")
	}
}
