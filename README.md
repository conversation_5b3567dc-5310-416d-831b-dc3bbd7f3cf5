# Qing Users Profiles

A user authentication HTTP server built with Go, Iris v12, Redis, and PostgreSQL.

## Features

- **Iris v12 Framework**: High-performance web framework
- **Redis Integration**: Session management and caching using rueidis client
- **PostgreSQL Support**: User data persistence with connection pooling
- **Standardized API**: Consistent JSON response format with code/data/msg/extra fields
- **Header Validation**: Required headers middleware (Authorization, X-Signature, X-Device-Id)
- **Health Checks**: Database connectivity monitoring
- **Graceful Shutdown**: Proper server lifecycle management
- **Environment Configuration**: Flexible configuration via environment variables

## Project Structure

```
qing-profiles/
├── config/          # Configuration management
├── controllers/     # HTTP request handlers
├── middleware/      # Custom middleware (headers, CORS, logging)
├── models/          # Data models and structures
├── utils/           # Utility functions (database, response helpers)
├── go.mod           # Go module dependencies
└── main.go          # Application entry point
```

## Dependencies

- **Web Framework**: `github.com/kataras/iris/v12`
- **Redis Client**: `github.com/redis/rueidis`
- **PostgreSQL Driver**: `github.com/lib/pq`
- **Configuration**: `github.com/joho/godotenv`
- **Logging**: `github.com/sirupsen/logrus`

