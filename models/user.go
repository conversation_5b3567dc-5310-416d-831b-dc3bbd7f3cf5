package models

import (
	"database/sql"
)

type UserCredentialDBItem struct {
	Qid       int64          `json:"qid"`
	CrdtType  int64          `json:"crdt_type"` // 1:pwd 2:jwt_token
	CrdtKey   string         `json:"crdt_key"`
	CrdtValue string         `json:"crdt_value"`
	CreatedAt int64          `json:"created_at"`
	ExpireAt  int64          `json:"expire_at"`
	Status    int64          `json:"status"`
	Extra     sql.NullString `json:"extra"`
}

type UserGenMediasDBItem struct {
	MediaId        int64          `json:"media_id"`
	Qid            int64          `json:"qid"`
	Scenario       int            `json:"scenario"`
	MediaType      int            `json:"media_type"`
	MediaObjectKey string         `json:"media_object_key"`
	FileSize       int64          `json:"file_size"`
	Status         int            `json:"status"`
	CreatedAt      int64          `json:"created_at"`
	ThumbHash      string         `json:"thumb_hash"`
	Width          int64          `json:"width"`
	Height         int64          `json:"height"`
	Duration       int64          `json:"duration"`
	VideoCover     string         `json:"video_cover"`
	Exif           sql.NullString `json:"exif"`
	OCR            sql.NullString `json:"ocr"`
	AiDesc         sql.NullString `json:"ai_desc"`
	Extra          sql.NullString `json:"extra"`
}

// UserBasicInfoDBItem 表示用户基本信息
type UserBasicInfoDBItem struct {
	Qid            int64          `json:"qid"`
	Phone          string         `json:"phone"`
	CustomQid      sql.NullString `json:"custom_qid"`
	Username       string         `json:"username"`
	AvatarId       int64          `json:"avatar_id"`
	Role           float32        `json:"role"`
	HasInitialized bool           `json:"has_initialized"`
}

type UserExtraProfileDBItem struct {
	Qid          int64  `json:"qid"`
	Bio          string `json:"bio"`
	Birthday     int64  `json:"birthday"`
	BirthdayBlur bool   `json:"birthday_blur"`
	ZodiacSign   int    `json:"zodiac_sign"`
	Height       int    `json:"height"`
	HeightBlur   bool   `json:"height_blur"`
	Weight       int    `json:"weight"`
	WeightBlur   bool   `json:"weight_blur"`
	CurrentCity  string `json:"current_city"`
	HomeTown     string `json:"home_town"`
	MBTI         int    `json:"mbti"`
}
