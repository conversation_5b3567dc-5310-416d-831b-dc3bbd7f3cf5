package models

// 用户角色
//-1:不选 0 1 2 3 4 5 6 7 8 9 10, 11:side 12:~

// 凭证类型常量
const (
	CredentialTypePassword = 1 // 密码凭证类型
	CredentialTypeJWT      = 2 // jwt_token凭证类型
)

// 星座
const (
	ZodiacSignAries       = 1  // 白羊座
	ZodiacSignTaurus      = 2  // 金牛座
	ZodiacSignGemini      = 3  // 双子座
	ZodiacSignCancer      = 4  // 巨蟹座
	ZodiacSignLeo         = 5  // 狮子座
	ZodiacSignVirgo       = 6  // 处女座
	ZodiacSignLibra       = 7  // 天秤座
	ZodiacSignScorpio     = 8  // 天蝎座
	ZodiacSignSagittarius = 9  // 射手座
	ZodiacSignCapricorn   = 10 // 摩羯座
	ZodiacSignAquarius    = 11 // 水瓶座
	ZodiacSignPisces      = 12 // 双鱼座
	ZodiacSignNone        = -1 // 未选择
)

// MBTI
const (
	MBTI_INTJ = 1  // INTJ
	MBTI_INTP = 2  // INTP
	MBTI_ENTJ = 3  // ENTJ
	MBTI_ENTP = 4  // ENTP
	MBTI_INFJ = 5  // INFJ
	MBTI_INFP = 6  // INFP
	MBTI_ENFJ = 7  // ENFJ
	MBTI_ENFP = 8  // ENFP
	MBTI_ISTJ = 9  // ISTJ
	MBTI_ISFJ = 10 // ISFJ
	MBTI_ESTJ = 11 // ESTJ
	MBTI_ESFJ = 12 // ESFJ
	MBTI_ISTP = 13 // ISTP
	MBTI_ISFP = 14 // ISFP
	MBTI_ESTP = 15 // ESTP
	MBTI_ESFP = 16 // ESFP
	MBTI_None = -1 // 未选择
)

// 用户资料标签
const (
	UserProfileTagTypePersonality           = 1 // 个性标签
	UserProfileTagTagTypeInterest           = 2 // 兴趣标签
	UserProfileTagTagTypeRelationshipStatus = 3 // 情感标签
	UserProfileTagTagTypeFutureGoal         = 4 // 未来预期标签
)

// 用户凭证状态
const (
	UserCredentialStatusNormal  = 0 // 正常有效
	UserCredentialStatusInvalid = 1 // 无效
)

// 分组类型常量
const (
	GroupTypeTemporary = 0 // 临时分组
	GroupTypePermanent = 1 // 固定分组
)

// 可见性类型常量
const (
	VisTypeSelf      = 0 // 私密
	VisTypeAll       = 1 // 公开
	VisTypeWhitelist = 2 // 部分人可见（白名单）
	VisTypeBlacklist = 3 // 部分人不可见（黑名单）
)

// 动态状态
const (
	FeedStatusDeleted         = -3 // -3:删除
	FeedStatusHumanRejected   = -2 // -2:人审不通过
	FeedStatusMachineRejected = -1 // -1:机审不通过
	FeedStatusDefault         = 0  // 0:未审核
	FeedStatusMachineApproved = 1  // 1:机审通过
	FeedStatusHumanApproved   = 2  // 2:人审通过
)

// 动态好友分组状态
const (
	FeedGroupStatusNormal  = 0 // 正常有效
	FeedGroupStatusDeleted = 1 // 无效
)

// 动态评论状态
const (
	FeedCommentStatusNormal  = 0 // 正常有效
	FeedCommentStatusDeleted = 1 // 已删除
)

// 用户关系类型常量
const (
	RelationshipTypeFriend  = 1 // 好友
	RelationshipTypeBlocked = 2 // 拉黑
)

// 审核资料表场景
const (
	PendingReviewProfileScenarioUpdateQingID       = 1 // 修改qingID
	PendingReviewProfileScenarioUpdateAvatar       = 2 // 修改头像
	PendingReviewProfileScenarioUpdateUsername     = 3 // 修改昵称
	PendingReviewProfileScenarioUpdateBio          = 4 // 修改简介
	PendingReviewProfileScenarioUpdateProfileCover = 5 // 修改个人主页封面
	PendingReviewProfileScenarioUpdateMomentsCover = 6 // 修改好友圈主页封面
)

// 审核资料表状态
const (
	PendingReviewProfileStatusCancelled = -2 // 已取消
	PendingReviewProfileStatusRejected  = -1 // 审核不通过
	PendingReviewProfileStatusPending   = 0  // 待审核
	PendingReviewProfileStatusApproved  = 1  // 审核通过
)

// 待审核动态状态
const (
	PendingReviewFeedStatusRejected = -1 // 审核不通过
	PendingReviewFeedStatusPending  = 0  // 待审核
	PendingReviewFeedStatusApproved = 1  // 审核通过
)

// 用户生成媒体场景
const (
	UserGenMediaScenarioNone         = 0 // 未指定场景
	UserGenMediaScenarioAvatar       = 1 // 设置头像
	UserGenMediaScenarioFeed         = 2 // 发布动态
	UserGenMediaScenarioProfileCover = 3 // 个人主页封面
	UserGenMediaScenarioMomentsCover = 4 // 好友圈封面
)

// 用户生成媒体状态
const (
	UserGenMediaStatusDeleted         = -3 // 已删除
	UserGenMediaStatusReviewRejected  = -2 // 人工审核不通过
	UserGenMediaStatusMachineRejected = -1 // 审核不通过
	UserGenMediaStatusPending         = 0  // 待审核
	UserGenMediaStatusMachineApproved = 1  // 机器审核通过
	UserGenMediaStatusUsed            = 2  // 已使用
	UserGenMediaStatusReviewed        = 3  // 已审核
)

// 动态分发任务状态
const (
	FeedFanoutTaskStatusFailed       = -1 // 处理失败
	FeedFanoutTaskStatusPending      = 0  // 待处理
	FeedFanoutTaskStatusDistributing = 1  // 分发中
	FeedFanoutTaskStatusDone         = 2  // 已完成
)

const (
	FeedFanoutTaskPriorityDefault = 0
)

// 动态分发任务场景
const (
	FeedFanoutScenarioPublishFeed = 1 // 发布动态
	FeedFanoutScenarioDeleteFeed  = 2 // 删除动态
	FeedFanoutScenarioUpdateFeed  = 3 // 修改动态可见范围
	FeedFanoutScenarioFriendship  = 4 // 好友关系变更
	FeedFanoutScenarioFeedGroup   = 5 // 修改好友分组
)

// 动态媒体类型
const (
	FeedMediaTypeImage = 1 // 图片
	FeedMediaTypeVideo = 2 // 视频
)

// 媒体类型常量
const (
	MediaTypeImage = 1 // 图片
	MediaTypeVideo = 2 // 视频
)

const (
	SecureVisLevel1 = 1 // 仅自己可见（好友、陌生人查看主页、公域均不可见）
	SecureVisLevel2 = 2 // 自己、好友可见（陌生人查看主页不可见、公域不可见）
	SecureVisLevel3 = 3 // 自己、好友、陌生人查看主页可见（公域不可见）
	SecureVisLevel4 = 4 // 自己、好友、陌生人查看主页、公域均可见
)
