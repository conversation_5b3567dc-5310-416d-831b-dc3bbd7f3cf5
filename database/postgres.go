package database

import (
	"context"
	"database/sql"
	"fmt"
	"qing-profiles/config"
	"time"

	_ "github.com/lib/pq"
	"github.com/sirupsen/logrus"
)

var thePostgreDB *sql.DB

// initPostgreSQL initializes PostgreSQL database connection
func initPostgreSQL(cfg *config.Config) error {
	// Build connection string
	destination := cfg.Postgresql.Destination
	maxOpenConns := cfg.Postgresql.MaxOpenConns
	maxIdleConns := cfg.Postgresql.MaxIdleConns
	connMaxLifetimeMinute := cfg.Postgresql.ConnMaxLifetimeMinute

	// Open a database connection
	db, err := sql.Open("postgres", destination)
	if err != nil {
		return fmt.Errorf("failed to open database connection: %w", err)
	}

	// Configure a connection pool
	db.SetMaxOpenConns(maxOpenConns)                                          // Maximum number of open connections
	db.SetMaxIdleConns(maxIdleConns)                                          // Maximum number of idle connections
	db.SetConnMaxLifetime(time.Duration(connMaxLifetimeMinute) * time.Minute) // Maximum connection lifetime

	// Test the connection
	if err := db.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	thePostgreDB = db

	logrus.WithFields(logrus.Fields{}).Info("PostgreSQL connection established")

	return nil
}

// ClosePostgreSQL closes the PostgreSQL database connection
func ClosePostgreSQL() error {
	if thePostgreDB != nil {
		logrus.Info("Closing PostgreSQL connection")
		return thePostgreDB.Close()
	}
	return nil
}

// HealthCheckPostgreSQL checks if the PostgreSQL connection is healthy
func HealthCheckPostgreSQL() error {
	if thePostgreDB == nil {
		return fmt.Errorf("database connection is nil")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := thePostgreDB.PingContext(ctx); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	return nil
}
func GetDB() *sql.DB {
	return thePostgreDB
}
