package config

import "os"

type Config struct {
	Server         ServerConfig
	Postgresql     PostgresqlConfig
	Redis          RedisConfig
	Pns            PnsConfig
	Sms            SmsConfig
	S4             S4Config
	Snowflake      SnowflakeConfig
	StaticResource StaticResourceConfig
}

func (c *Config) IsProduction() bool {
	return c.Server.Env == "PRO"
}

type ServerConfig struct {
	Env  string
	Port string
}

type PostgresqlConfig struct {
	Destination           string
	MaxOpenConns          int
	MaxIdleConns          int
	ConnMaxLifetimeMinute int
}

type RedisConfig struct {
	Addr     string
	Username string
	Password string
	DB       int
}
type PnsConfig struct {
	AccessKeyId     string
	AccessKeySecret string
}
type SmsConfig struct {
	AccessKeyId     string
	AccessKeySecret string
	SignName        string
	TemplateCode    string
}
type S4Config struct {
	AK     string
	SK     string
	Bucket string
}
type SnowflakeConfig struct {
	NodeId       int64
	BackupNodeId int64
}
type StaticResourceConfig struct {
	Domain string
}

var projectConfig *Config = nil

// GetConfig 获取配置
func GetConfig() *Config {
	if projectConfig == nil {
		loadConfig()
	}
	return projectConfig
}

// 加载配置
func loadConfig() {
	env := os.Getenv("ENV")

	// 正式环境
	if env == "PRO" {
		projectConfig = &Config{
			Server: ServerConfig{
				Env:  "PRO",
				Port: "8003",
			},
			Postgresql: PostgresqlConfig{
				Destination: "",
			},
			Redis: RedisConfig{
				Addr:     "",
				Password: "",
				DB:       0,
			},
			Pns: PnsConfig{
				AccessKeyId:     "",
				AccessKeySecret: "",
			},
			Sms: SmsConfig{
				AccessKeyId:     "",
				AccessKeySecret: "",
				SignName:        "",
				TemplateCode:    "",
			},
			S4: S4Config{
				AK:     "",
				SK:     "",
				Bucket: "",
			},
			Snowflake: SnowflakeConfig{
				NodeId:       3,
				BackupNodeId: 203,
			},
			StaticResource: StaticResourceConfig{
				Domain: "",
			},
		}
	}

	// 测试环境
	projectConfig = &Config{
		Server: ServerConfig{
			Env:  "DEV",
			Port: "8003",
		},
		Postgresql: PostgresqlConfig{
			Destination: "host=pgm-uf64r7i42r8qw4xi.pg.rds.aliyuncs.com port=5432 user=qcyl password=QCYL20250721! dbname=qing sslmode=disable",
		},
		Redis: RedisConfig{
			Addr:     "r-uf6xlnxky5wcyotb8u.redis.rds.aliyuncs.com:6379",
			Username: "qcyl",
			Password: "rQCYL20250721!",
			DB:       0,
		},
		Pns: PnsConfig{
			AccessKeyId:     "LTAI5tS5Mn8Yzt89P1szKyGe",
			AccessKeySecret: "******************************",
		},
		Sms: SmsConfig{
			AccessKeyId:     "LTAI5tS5Mn8Yzt89P1szKyGe",
			AccessKeySecret: "******************************",
			SignName:        "青初于蓝",
			TemplateCode:    "SMS_323345406",
		},
		S4: S4Config{
			AK:     "WYROz7zZbhK05EE1moQwJZJV",
			SK:     "GaNXHPhZPUVYXp3coxSEBiqcUwx4FmV",
			Bucket: "ethan0115",
		},
		Snowflake: SnowflakeConfig{
			NodeId:       3,
			BackupNodeId: 203,
		},
		StaticResource: StaticResourceConfig{
			Domain: "qing-test-files.bilifans.com",
		},
	}
}
