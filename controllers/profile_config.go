package controllers

import (
	"fmt"
	"qing-profiles/config"
	"qing-profiles/utils"
	"time"

	"github.com/kataras/iris/v12"
)

// ProfileConfigController handles app configuration-related endpoints
type ProfileConfigController struct{}

// NewProfileConfigController creates a new profile config controller
func NewProfileConfigController() *ProfileConfigController {
	return &ProfileConfigController{}
}

type GetBirthdayConfigResponse struct {
	List []YearGroup `json:"list"`
}

// GetBirthdayConfig
// 获取生日配置
func (p *ProfileConfigController) GetBirthdayConfig(ctx iris.Context) {
	year := time.Now().Year()
	minYear := year - 100 // 最大100岁
	maxYear := year - 19  // 最小19岁

	list := generateYearGroups(minYear, maxYear)
	response := GetBirthdayConfigResponse{
		List: list,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

type GetZodiacSignConfigResponse struct {
	List          []ZodiacSign `json:"list"`
	BackgroundUrl string       `json:"background_url"`
}

// GetZodiacSignConfig
// 获取星座配置
func (p *ProfileConfigController) GetZodiacSignConfig(ctx iris.Context) {
	domain := config.GetConfig().StaticResource.Domain

	response := GetZodiacSignConfigResponse{
		List:          zodiacSignConfig,
		BackgroundUrl: fmt.Sprintf("https://%v/app/zodiac-sign/zodiac-sign-bg.jpg", domain),
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

type GetHeightConfigResponse struct {
	List []HeightGroup `json:"list"`
}

// GetHeightConfig
// 获取生日配置
func (p *ProfileConfigController) GetHeightConfig(ctx iris.Context) {
	response := GetHeightConfigResponse{
		List: heightConfig,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

type GetWeightConfigResponse struct {
	List []WeightGroup `json:"list"`
}

// GetWeightConfig
// 获取体重配置
func (p *ProfileConfigController) GetWeightConfig(ctx iris.Context) {
	response := GetWeightConfigResponse{
		List: weightConfig,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

type GetMBTIConfigResponse struct {
	List []MBTI `json:"list"`
}

// GetMBTIConfig
// 获取MBTI配置
func (p *ProfileConfigController) GetMBTIConfig(ctx iris.Context) {
	response := GetMBTIConfigResponse{
		List: mbtiConfig,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

type GetPersonalityTagsConfigResponse struct {
	List     []PersonalityTagGroup `json:"list"`
	MaxCount int                   `json:"max_count"`
}

func (p *ProfileConfigController) GetPersonalitiesConfig(ctx iris.Context) {
	response := GetPersonalityTagsConfigResponse{
		List:     personalityTagGroups,
		MaxCount: 12,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

type GetInterestsConfigResponse struct {
	List     []InterestGroup `json:"list"`
	MaxCount int             `json:"max_count"`
}
type InterestGroup struct {
	Tags      []Interest `json:"tags"`
	GroupName string     `json:"group_name"`
}

type Interest struct {
	Id    string `json:"id"`
	Name  string `json:"name"`
	Color string `json:"color"`
}

func (p *ProfileConfigController) GetInterestsConfig(ctx iris.Context) {
	response := GetInterestsConfigResponse{
		List:     interestGroups,
		MaxCount: 12,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

type GetRelationshipStatusConfigResponse struct {
	List     []RelationshipStatusGroup `json:"list"`
	MaxCount int                       `json:"max_count"`
}

type RelationshipStatusGroup struct {
	GroupName string               `json:"group_name"`
	Tags      []RelationshipStatus `json:"tags"`
}

type RelationshipStatus struct {
	Id    string `json:"id"`
	Name  string `json:"name"`
	Color string `json:"color"`
}

func (p *ProfileConfigController) GetRelationshipStatusConfig(ctx iris.Context) {
	response := GetRelationshipStatusConfigResponse{
		List:     relationshipStatusGroups,
		MaxCount: 12,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

type GetFutureGoalsConfigResponse struct {
	List     []FutureGoalGroup `json:"list"`
	MaxCount int               `json:"max_count"`
}
type FutureGoalGroup struct {
	GroupName string       `json:"group_name"`
	Tags      []FutureGoal `json:"tags"`
}

type FutureGoal struct {
	Id    string `json:"id"`
	Name  string `json:"name"`
	Color string `json:"color"`
}

func (p *ProfileConfigController) GetFutureGoalsConfig(ctx iris.Context) {
	response := GetFutureGoalsConfigResponse{
		List:     futureGoalGroups,
		MaxCount: 12,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}
