package controllers

import (
	"fmt"
	"qing-profiles/config"
	"qing-profiles/utils"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

// ProfileConfigController handles app configuration-related endpoints
type ProfileConfigController struct{}

// NewProfileConfigController creates a new profile config controller
func NewProfileConfigController() *ProfileConfigController {
	return &ProfileConfigController{}
}

type GetBirthdayConfigResponse struct {
	List []YearGroup `json:"list"`
}

// GetBirthdayConfig
// 获取生日配置
func (p *ProfileConfigController) GetBirthdayConfig(ctx iris.Context) {
	year := time.Now().Year()
	minYear := year - 100 // 最大100岁
	maxYear := year - 19  // 最小19岁

	list := generateYearGroups(minYear, maxYear)
	response := GetBirthdayConfigResponse{
		List: list,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

type GetZodiacSignConfigResponse struct {
	List          []*ZodiacSign `json:"list"`
	BackgroundUrl string        `json:"background_url"`
}

type ZodiacSign struct {
	Id           string `json:"id"`
	Name         string `json:"name"`
	Icon         string `json:"icon"`
	SelectedIcon string `json:"selected_icon"`
}

// GetZodiacSignConfig
// 获取星座配置
func (p *ProfileConfigController) GetZodiacSignConfig(ctx iris.Context) {
	list := make([]*ZodiacSign, 0)
	list = append(list, &ZodiacSign{
		Id:   "aries",
		Name: "白羊座",
	}, &ZodiacSign{
		Id:   "taurus",
		Name: "金牛座",
	}, &ZodiacSign{
		Id:   "gemini",
		Name: "双子座",
	}, &ZodiacSign{
		Id:   "cancer",
		Name: "巨蟹座",
	}, &ZodiacSign{
		Id:   "leo",
		Name: "狮子座",
	}, &ZodiacSign{
		Id:   "virgo",
		Name: "处女座",
	}, &ZodiacSign{
		Id:   "libra",
		Name: "天秤座",
	}, &ZodiacSign{
		Id:   "scorpio",
		Name: "天蝎座",
	}, &ZodiacSign{
		Id:   "sagittarius",
		Name: "射手座",
	}, &ZodiacSign{
		Id:   "capricorn",
		Name: "摩羯座",
	}, &ZodiacSign{
		Id:   "aquarius",
		Name: "水瓶座",
	}, &ZodiacSign{
		Id:   "pisces",
		Name: "双鱼座",
	}, &ZodiacSign{
		Id:   "none",
		Name: "不选",
	})

	domain := config.GetConfig().StaticResource.Domain
	for _, item := range list {
		item.Icon = fmt.Sprintf("https://%v/app/zodiac-sign/%v-0.svg?fmt=tpng", domain, item.Id)
		item.SelectedIcon = fmt.Sprintf("https://%v/app/zodiac-sign/%v-1.svg?fmt=tpng", domain, item.Id)
	}

	response := GetZodiacSignConfigResponse{
		List:          list,
		BackgroundUrl: "https://qing-test-files.bilifans.com/app/zodiac-sign/zodiac-sign-bg.jpg",
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

type HeightGroup struct {
	Name    string   `json:"name"`
	Heights []string `json:"heights"`
}
type GetHeightConfigResponse struct {
	List []HeightGroup `json:"list"`
}

// GetHeightConfig
// 获取生日配置
func (p *ProfileConfigController) GetHeightConfig(ctx iris.Context) {
	//minHeight := 220 // 最大220cm
	//maxHeight := 120 // 最小120cm

	list := make([]HeightGroup, 0)
	list = append(list, HeightGroup{
		Name:    "120",
		Heights: []string{"120", "121", "122", "123", "124", "125", "126", "127", "128", "129"},
	})
	list = append(list, HeightGroup{
		Name:    "130",
		Heights: []string{"130", "131", "132", "133", "134", "135", "136", "137", "138", "139"},
	})
	list = append(list, HeightGroup{
		Name:    "140",
		Heights: []string{"140", "141", "142", "143", "144", "145", "146", "147", "148", "149"},
	})
	list = append(list, HeightGroup{
		Name:    "150",
		Heights: []string{"150", "151", "152", "153", "154", "155", "156", "157", "158", "159"},
	})
	list = append(list, HeightGroup{
		Name:    "160",
		Heights: []string{"160", "161", "162", "163", "164", "165", "166", "167", "168", "169"},
	})
	list = append(list, HeightGroup{
		Name:    "170",
		Heights: []string{"170", "171", "172", "173", "174", "175", "176", "177", "178", "179"},
	})
	list = append(list, HeightGroup{
		Name:    "180",
		Heights: []string{"180", "181", "182", "183", "184", "185", "186", "187", "188", "189"},
	})
	list = append(list, HeightGroup{
		Name:    "190",
		Heights: []string{"190", "191", "192", "193", "194", "195", "196", "197", "198", "199"},
	})
	list = append(list, HeightGroup{
		Name:    "200",
		Heights: []string{"200", "201", "202", "203", "204", "205", "206", "207", "208", "209"},
	})
	list = append(list, HeightGroup{
		Name:    "210",
		Heights: []string{"210", "211", "212", "213", "214", "215", "216", "217", "218", "219"},
	})
	list = append(list, HeightGroup{
		Name:    "220",
		Heights: []string{"220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "none"},
	})

	response := GetHeightConfigResponse{
		List: list,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

type WeightGroup struct {
	Name    string   `json:"name"`
	Weights []string `json:"weights"`
}
type GetWeightConfigResponse struct {
	List []WeightGroup `json:"list"`
}

// GetWeightConfig
// 获取体重配置
func (p *ProfileConfigController) GetWeightConfig(ctx iris.Context) {
	list := make([]WeightGroup, 0)
	list = append(list, WeightGroup{
		Name:    "40",
		Weights: []string{"40", "41", "42", "43", "44", "45", "46", "47", "48", "49"},
	})
	list = append(list, WeightGroup{
		Name:    "50",
		Weights: []string{"50", "51", "52", "53", "54", "55", "56", "57", "58", "59"},
	})
	list = append(list, WeightGroup{
		Name:    "60",
		Weights: []string{"60", "61", "62", "63", "64", "65", "66", "67", "68", "69"},
	})
	list = append(list, WeightGroup{
		Name:    "70",
		Weights: []string{"70", "71", "72", "73", "74", "75", "76", "77", "78", "79"},
	})
	list = append(list, WeightGroup{
		Name:    "80",
		Weights: []string{"80", "81", "82", "83", "84", "85", "86", "87", "88", "89"},
	})
	list = append(list, WeightGroup{
		Name:    "90",
		Weights: []string{"90", "91", "92", "93", "94", "95", "96", "97", "98", "99"},
	})
	list = append(list, WeightGroup{
		Name:    "100",
		Weights: []string{"100", "101", "102", "103", "104", "105", "106", "107", "108", "109"},
	})
	list = append(list, WeightGroup{
		Name:    "110",
		Weights: []string{"110", "111", "112", "113", "114", "115", "116", "117", "118", "119"},
	})
	list = append(list, WeightGroup{
		Name:    "120",
		Weights: []string{"120", "121", "122", "123", "124", "125", "126", "127", "128", "129", "none"},
	})

	response := GetWeightConfigResponse{
		List: list,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

type GetMBTIConfigResponse struct {
	List []MBTI `json:"list"`
}

type MBTI struct {
	Id           string `json:"id"`
	Name         string `json:"name"`
	Icon         string `json:"icon"`
	SelectedIcon string `json:"selected_icon"`
}

// GetMBTIConfig
// 获取MBTI配置
func (p *ProfileConfigController) GetMBTIConfig(ctx iris.Context) {
	list := make([]MBTI, 0)
	tags := []string{"INTJ", "INTP", "ENTJ", "ENTP", "ISTJ", "INFJ", "INFP", "FJ", "ENFP", "ISTJ", "ISFJ", "ESTJ", "ESFJ", "ISTP", "ISFP", "ESFP", "ESTP", "none"}
	domain := config.GetConfig().StaticResource.Domain

	for _, tag := range tags {
		lowerTag := strings.ToLower(tag)
		item := MBTI{
			Id:           lowerTag,
			Name:         tag,
			Icon:         fmt.Sprintf("https://%v/app/mbti/%v-0.png?fmt=tpng", domain, lowerTag),
			SelectedIcon: fmt.Sprintf("https://%v/app/mbti/%v-1.png?fmt=tpng", domain, lowerTag),
		}
		if tag == "none" {
			item.Name = "不选"
		}
		list = append(list, item)
	}

	response := GetMBTIConfigResponse{
		List: list,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

type GetPersonalityTagsConfigResponse struct {
	List     []*PersonalityTagGroup `json:"list"`
	MaxCount int                    `json:"max_count"`
}

type PersonalityTagGroup struct {
	GroupName string           `json:"group_name"`
	Tags      []PersonalityTag `json:"tags"`
}

type PersonalityTag struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

func (p *ProfileConfigController) GetPersonalitiesConfig(ctx iris.Context) {
	list := make([]*PersonalityTagGroup, 0)
	list = append(list, &PersonalityTagGroup{
		GroupName: "特质",
		Tags: []PersonalityTag{
			{
				Id:   "1",
				Name: "人夫脸",
			},
			{
				Id:   "2",
				Name: "青熟小叔",
			},
			{
				Id:   "3",
				Name: "体育生",
			},
		},
	}, &PersonalityTagGroup{
		GroupName: "身材",
		Tags: []PersonalityTag{
			{
				Id:   "4",
				Name: "肌肉",
			},
			{
				Id:   "5",
				Name: "薄肌",
			},
			{
				Id:   "6",
				Name: "高挑",
			},
		},
	}, &PersonalityTagGroup{
		GroupName: "身份",
		Tags: []PersonalityTag{
			{
				Id:   "7",
				Name: "警察",
			},
			{
				Id:   "8",
				Name: "公务员",
			},
			{
				Id:   "9",
				Name: "医生",
			},
			{
				Id:   "10",
				Name: "CEO",
			},
			{
				Id:   "11",
				Name: "打工人",
			},
			{
				Id:   "12",
				Name: "自由职业",
			},
		},
	})

	response := GetPersonalityTagsConfigResponse{
		List:     list,
		MaxCount: 12,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

type GetInterestsConfigResponse struct {
	List     []InterestGroup `json:"list"`
	MaxCount int             `json:"max_count"`
}
type InterestGroup struct {
	Tags      []Interest `json:"tags"`
	GroupName string     `json:"group_name"`
}

type Interest struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

func (p *ProfileConfigController) GetInterestsConfig(ctx iris.Context) {
	list := make([]InterestGroup, 0)
	list = append(list,
		InterestGroup{
			GroupName: "运动",
			Tags: []Interest{
				{
					Id:   "1",
					Name: "网球",
				},
				{
					Id:   "2",
					Name: "飞盘",
				},
				{
					Id:   "3",
					Name: "骑行",
				},
				{
					Id:   "4",
					Name: "健身",
				},
				{
					Id:   "5",
					Name: "跑步",
				},
			},
		},
		InterestGroup{
			GroupName: "偏好",
			Tags: []Interest{
				{
					Id:   "6",
					Name: "麻将",
				},
				{
					Id:   "7",
					Name: "旅行",
				},
			},
		},
		InterestGroup{
			GroupName: "饮食",
			Tags: []Interest{
				{
					Id:   "8",
					Name: "咖啡",
				},
				{
					Id:   "9",
					Name: "火锅",
				},
			},
		},
	)

	response := GetInterestsConfigResponse{
		List:     list,
		MaxCount: 12,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

type GetRelationshipStatusConfigResponse struct {
	List     []*RelationshipStatusGroup `json:"list"`
	MaxCount int                        `json:"max_count"`
}

type RelationshipStatusGroup struct {
	GroupName string               `json:"group_name"`
	Tags      []RelationshipStatus `json:"tags"`
}

type RelationshipStatus struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

func (p *ProfileConfigController) GetRelationshipStatusConfig(ctx iris.Context) {
	list := make([]*RelationshipStatusGroup, 0)
	list = append(list, &RelationshipStatusGroup{
		GroupName: "情感状态",
		Tags: []RelationshipStatus{
			{
				Id:   "1",
				Name: "单身",
			},
			{
				Id:   "2",
				Name: "有对象",
			},
			{
				Id:   "3",
				Name: "夫夫生活",
			},
		},
	}, &RelationshipStatusGroup{
		GroupName: "孩子情况",
		Tags: []RelationshipStatus{
			{
				Id:   "4",
				Name: "无",
			},
			{
				Id:   "5",
				Name: "男娃",
			},
			{
				Id:   "6",
				Name: "女娃",
			},
		},
	}, &RelationshipStatusGroup{
		GroupName: "现在想要",
		Tags: []RelationshipStatus{
			{
				Id:   "7",
				Name: "找男友",
			},
			{
				Id:   "8",
				Name: "找朋友",
			},
			{
				Id:   "9",
				Name: "随便看看",
			},
		},
	})

	response := GetRelationshipStatusConfigResponse{
		List:     list,
		MaxCount: 12,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

type GetFutureGoalsConfigResponse struct {
	List     []*FutureGoalGroup `json:"list"`
	MaxCount int                `json:"max_count"`
}
type FutureGoalGroup struct {
	GroupName string       `json:"group_name"`
	Tags      []FutureGoal `json:"tags"`
}

type FutureGoal struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

func (p *ProfileConfigController) GetFutureGoalsConfig(ctx iris.Context) {
	list := make([]*FutureGoalGroup, 0)
	list = append(list, &FutureGoalGroup{
		GroupName: "出柜情况",
		Tags: []FutureGoal{
			{
				Id:   "1",
				Name: "对所有人出柜",
			},
			{
				Id:   "2",
				Name: "仅对父母出柜",
			},
			{
				Id:   "3",
				Name: "仅对同学朋友出柜",
			},
			{
				Id:   "4",
				Name: "不准备出柜",
			},
		},
	}, &FutureGoalGroup{
		GroupName: "婚姻计划",
		Tags: []FutureGoal{
			{
				Id:   "5",
				Name: "不打算",
			},
			{
				Id:   "6",
				Name: "要形婚",
			},
			{
				Id:   "7",
				Name: "已婚",
			},
			{
				Id:   "8",
				Name: "已离异",
			},
		},
	}, &FutureGoalGroup{
		GroupName: "要孩计划",
		Tags: []FutureGoal{
			{
				Id:   "9",
				Name: "不打算",
			},
			{
				Id:   "10",
				Name: "要一个",
			},
		},
	})

	response := GetFutureGoalsConfigResponse{
		List:     list,
		MaxCount: 12,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

//api.Get("/users/profile/config/zodiac-sign", profileConfigController.GetBirthdayConfig) // 获取星座配置
//api.Get("/users/profile/config/height", profileConfigController.GetBirthdayConfig)      // 获取身高配置
//api.Get("/users/profile/config/weight", profileConfigController.GetBirthdayConfig)      // 获取体重配置
//api.Get("/users/profile/config/roles", profileController.GetRoles)                      // 获取角色配置
//api.Get("/users/profile/config/current-city", profileController.GetRoles)               // 获取现居地配置
//api.Get("/users/profile/config/hometown", profileController.GetRoles)                   // 获取家乡配置
//api.Get("/users/profile/config/mbti", profileController.GetRoles)                       // 获取MBTI配置
//api.Get("/users/profile/config/personality-tags", profileController.GetRoles)           // 获取个性标签配置
//api.Get("/users/profile/config/interests", profileController.GetRoles)                  // 获取兴趣配置
//api.Get("/users/profile/config/relationship-status", profileController.GetRoles)        // 获取情感状态配置
//api.Get("/users/profile/config/future-goals", profileController.GetRoles)               // 获取未来预期配置
