package controllers

import (
	"fmt"
	"qing-profiles/config"
	"strings"
)

var zodiacSignConfig []ZodiacSign
var heightConfig []HeightGroup
var weightConfig []WeightGroup
var mbtiConfig []MBTI
var personalityTagGroups []PersonalityTagGroup
var interestGroups []InterestGroup
var relationshipStatusGroups []RelationshipStatusGroup
var futureGoalGroups []FutureGoalGroup

type ZodiacSign struct {
	Id           string `json:"id"`
	Name         string `json:"name"`
	Icon         string `json:"icon"`
	SelectedIcon string `json:"selected_icon"`
	TagColor     string `json:"tag_color"` // 作为tag展示时的颜色
}
type HeightGroup struct {
	Name    string   `json:"name"`
	Heights []string `json:"heights"`
}

type WeightGroup struct {
	Name    string   `json:"name"`
	Weights []string `json:"weights"`
}
type MBTI struct {
	Id           string `json:"id"`
	Name         string `json:"name"`
	Icon         string `json:"icon"`
	SelectedIcon string `json:"selected_icon"`
}
type PersonalityTagGroup struct {
	GroupName string           `json:"group_name"`
	Tags      []PersonalityTag `json:"tags"`
}

type PersonalityTag struct {
	Id    string `json:"id"`
	Name  string `json:"name"`
	Color string `json:"color"`
}

func init() {
	domain := config.GetConfig().StaticResource.Domain

	// 星座配置
	{
		zodiacSignMeta := []map[string]string{
			{"id": "aries", "name": "白羊座"},
			{"id": "taurus", "name": "金牛座"},
			{"id": "gemini", "name": "双子座"},
			{"id": "cancer", "name": "巨蟹座"},
			{"id": "leo", "name": "狮子座"},
			{"id": "virgo", "name": "处女座"},
			{"id": "libra", "name": "天秤座"},
			{"id": "scorpio", "name": "天蝎座"},
			{"id": "sagittarius", "name": "射手座"},
			{"id": "capricorn", "name": "摩羯座"},
			{"id": "aquarius", "name": "水瓶座"},
			{"id": "pisces", "name": "双鱼座"},
			{"id": "none", "name": "不选"},
		}

		zodiacSignConfig = make([]ZodiacSign, 0)
		for _, item := range zodiacSignMeta {
			zodiacSignConfig = append(zodiacSignConfig, ZodiacSign{
				Id:           item["id"],
				Name:         item["name"],
				Icon:         fmt.Sprintf("https://%v/app/zodiac-sign/%v-0.svg?fmt=tpng", domain, item["id"]),
				SelectedIcon: fmt.Sprintf("https://%v/app/zodiac-sign/%v-1.svg?fmt=tpng", domain, item["id"]),
			})
		}
	}

	// 身高配置
	{
		minHeight := 120
		maxHeight := 220
		heightConfig = make([]HeightGroup, 0)
		for i := minHeight; i <= maxHeight; i = i + 10 {
			group := HeightGroup{
				Name:    fmt.Sprintf("%d", i),
				Heights: []string{},
			}
			for j := 0; j < 10; j++ {
				group.Heights = append(group.Heights, fmt.Sprintf("%d", i+j))
			}
			if i == maxHeight {
				group.Heights = append(group.Heights, "none")
			}
			heightConfig = append(heightConfig, group)
		}
	}
	// 体重配置
	{

		minWeight := 40
		maxWeight := 120
		weightConfig = make([]WeightGroup, 0)
		for i := minWeight; i <= maxWeight; i = i + 10 {
			group := WeightGroup{
				Name:    fmt.Sprintf("%d", i),
				Weights: []string{},
			}
			for j := 0; j < 10; j++ {
				group.Weights = append(group.Weights, fmt.Sprintf("%d", i+j))
			}
			if i == maxWeight {
				group.Weights = append(group.Weights, "none")
			}
			weightConfig = append(weightConfig, group)
		}
	}

	// MBTI配置
	{
		mbtiConfig = make([]MBTI, 0)
		tags := []string{"INTJ", "INTP", "ENTJ", "ENTP", "INFJ", "INFP", "ENFJ", "ENFP", "ISTJ", "ISFJ", "ESTJ", "ESFJ", "ISTP", "ISFP", "ESTP", "ESFP", "none"}
		for _, tag := range tags {
			lowerTag := strings.ToLower(tag)
			item := MBTI{
				Id:           lowerTag,
				Name:         tag,
				Icon:         fmt.Sprintf("https://%v/app/mbti/%v-0.png?fmt=tpng", domain, lowerTag),
				SelectedIcon: fmt.Sprintf("https://%v/app/mbti/%v-1.png?fmt=tpng", domain, lowerTag),
			}
			if tag == "none" {
				item.Name = "不选"
			}
			mbtiConfig = append(mbtiConfig, item)
		}
	}

	// 个性标签配置
	{
		// TODO 我估计后续这些标签都需要可以在运营后台编辑的，所以暂时先写死
		personalityTagGroups = []PersonalityTagGroup{
			{GroupName: "特质", Tags: []PersonalityTag{{Id: "1", Name: "人夫脸", Color: "#E3FAFF"}, {Id: "2", Name: "青熟小叔", Color: "#E3FAFF"}, {Id: "3", Name: "体育生", Color: "#E3FAFF"}}},
			{GroupName: "身材", Tags: []PersonalityTag{{Id: "4", Name: "肌肉", Color: "#E3FAFF"}, {Id: "5", Name: "薄肌", Color: "#E3FAFF"}, {Id: "6", Name: "高挑", Color: "#E3FAFF"}}},
			{GroupName: "身份", Tags: []PersonalityTag{{Id: "7", Name: "警察", Color: "#E3FAFF"}, {Id: "8", Name: "公务员", Color: "#E3FAFF"}, {Id: "9", Name: "医生", Color: "#E3FAFF"}, {Id: "10", Name: "CEO", Color: "#E3FAFF"}, {Id: "11", Name: "打工人", Color: "#E3FAFF"}, {Id: "12", Name: "自由职业", Color: "#E3FAFF"}}},
		}
	}
	// 兴趣配置
	{
		interestGroups = []InterestGroup{
			{GroupName: "运动", Tags: []Interest{{Id: "1", Name: "网球", Color: "#E3FAFF"}, {Id: "2", Name: "飞盘", Color: "#E3FAFF"}, {Id: "3", Name: "骑行", Color: "#E3FAFF"}, {Id: "4", Name: "健身", Color: "#E3FAFF"}, {Id: "5", Name: "跑步", Color: "#E3FAFF"}}},
			{GroupName: "偏好", Tags: []Interest{{Id: "6", Name: "麻将", Color: "#E3FAFF"}, {Id: "7", Name: "旅行", Color: "#E3FAFF"}}},
			{GroupName: "饮食", Tags: []Interest{{Id: "8", Name: "咖啡", Color: "#E3FAFF"}, {Id: "9", Name: "火锅", Color: "#E3FAFF"}}},
		}
	}

	// 情感状态配置
	{
		relationshipStatusGroups = []RelationshipStatusGroup{
			{GroupName: "情感状态", Tags: []RelationshipStatus{{Id: "1", Name: "单身", Color: "#E3FAFF"}, {Id: "2", Name: "有对象", Color: "#E3FAFF"}, {Id: "3", Name: "夫夫生活", Color: "#E3FAFF"}}},
			{GroupName: "孩子情况", Tags: []RelationshipStatus{{Id: "4", Name: "无", Color: "#E3FAFF"}, {Id: "5", Name: "男娃", Color: "#E3FAFF"}, {Id: "6", Name: "女娃", Color: "#E3FAFF"}}},
			{GroupName: "现在想要", Tags: []RelationshipStatus{{Id: "7", Name: "找男友", Color: "#E3FAFF"}, {Id: "8", Name: "找朋友", Color: "#E3FAFF"}, {Id: "9", Name: "随便看看", Color: "#E3FAFF"}}},
		}
	}
	// 未来发展配置
	{
		futureGoalGroups = []FutureGoalGroup{
			{GroupName: "出柜情况", Tags: []FutureGoal{{Id: "1", Name: "对所有人出柜", Color: "#E3FAFF"}, {Id: "2", Name: "仅对父母出柜", Color: "#E3FAFF"}, {Id: "3", Name: "仅对同学朋友出柜", Color: "#E3FAFF"}, {Id: "4", Name: "不准备出柜", Color: "#E3FAFF"}}},
			{GroupName: "婚姻计划", Tags: []FutureGoal{{Id: "5", Name: "不打算", Color: "#E3FAFF"}, {Id: "6", Name: "要形婚", Color: "#E3FAFF"}, {Id: "7", Name: "已婚", Color: "#E3FAFF"}, {Id: "8", Name: "已离异", Color: "#E3FAFF"}}},
			{GroupName: "要孩计划", Tags: []FutureGoal{{Id: "9", Name: "不打算", Color: "#E3FAFF"}, {Id: "10", Name: "要一个", Color: "#E3FAFF"}, {Id: "11", Name: "要两个", Color: "#E3FAFF"}, {Id: "12", Name: "要三个及以上", Color: "#E3FAFF"}}},
		}
	}
}

// 根据星座id获取星座信息
func getZodiacSignById(id string) *ZodiacSign {
	for _, item := range zodiacSignConfig {
		if item.Id == id {
			return &item
		}
	}
	return nil
}

// 根据身高id获取身高信息
func getHeightById(id string) *HeightGroup {
	for _, item := range heightConfig {
		for _, height := range item.Heights {
			if height == id {
				return &item
			}
		}
	}
	return nil
}

// 根据体重id获取体重信息
func getWeightById(id string) *WeightGroup {
	for _, item := range weightConfig {
		for _, weight := range item.Weights {
			if weight == id {
				return &item
			}
		}
	}
	return nil
}

// 根据MBTIid获取MBTI信息
func getMBTIById(id string) *MBTI {
	for _, item := range mbtiConfig {
		if item.Id == id {
			return &item
		}
	}
	return nil
}

// 根据个性标签id获取个性标签信息
func getPersonalityTagById(id string) *PersonalityTag {
	for _, group := range personalityTagGroups {
		for _, tag := range group.Tags {
			if tag.Id == id {
				return &tag
			}
		}
	}
	return nil
}

// 根据兴趣id获取兴趣信息
func getInterestById(id string) *Interest {
	for _, group := range interestGroups {
		for _, tag := range group.Tags {
			if tag.Id == id {
				return &tag
			}
		}
	}
	return nil
}

// 根据情感状态id获取情感状态信息
func getRelationshipStatusById(id string) *RelationshipStatus {
	for _, group := range relationshipStatusGroups {
		for _, tag := range group.Tags {
			if tag.Id == id {
				return &tag
			}
		}
	}
	return nil
}

// 根据未来发展id获取未来发展信息
func getFutureGoalById(id string) *FutureGoal {
	for _, group := range futureGoalGroups {
		for _, tag := range group.Tags {
			if tag.Id == id {
				return &tag
			}
		}
	}
	return nil
}
