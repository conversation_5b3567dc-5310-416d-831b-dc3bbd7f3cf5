package controllers

import (
	"qing-profiles/utils"

	"github.com/sirupsen/logrus"

	"github.com/kataras/iris/v12"
)

type GetRegionsConfigResponse struct {
	List []*utils.RegionProvince `json:"list"`
}

// GetRegionsConfig
// 获取地区配置
func (p *ProfileConfigController) GetRegionsConfig(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	regions := utils.GetAllRegions()
	if len(regions) <= 0 {
		logrus.WithFields(logrus.Fields{
			"qid": qid,
		}).Error("GetRegionsConfig 获取省份失败")
		utils.InternalError(ctx, "")
		return
	}
	response := GetRegionsConfigResponse{
		List: regions,
	}
	utils.SuccessWithMsg(ctx, response, "")
	return
}
