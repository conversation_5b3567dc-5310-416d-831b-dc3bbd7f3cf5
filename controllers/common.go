package controllers

import (
	"database/sql"
	"errors"
	"fmt"
	"qing-profiles/database"
	"qing-profiles/models"
	"qing-profiles/utils"
	"time"

	gonanoid "github.com/matoous/go-nanoid/v2"
)

var qingUserRoles = []UserRole{
	{
		RoleName: "不选",
		RoleId:   "none",
	},
	{
		RoleName: "0",
		RoleId:   "0",
	},
	{
		RoleName: "0.1",
		RoleId:   "0.1",
	},
	{
		RoleName: "0.2",
		RoleId:   "0.2",
	},
	{
		RoleName: "0.3",
		RoleId:   "0.3",
	},
	{
		RoleName: "0.4",
		RoleId:   "0.4",
	},
	{
		RoleName: "0.5",
		RoleId:   "0.5",
	},
	{
		RoleName: "0.6",
		RoleId:   "0.6",
	},
	{
		RoleName: "0.7",
		RoleId:   "0.7",
	},
	{
		RoleName: "0.8",
		RoleId:   "0.8",
	},
	{
		RoleName: "0.9",
		RoleId:   "0.9",
	},
	{
		RoleName: "1",
		RoleId:   "1",
	},
	{
		RoleName: "Side",
		RoleId:   "side",
	},
	{
		RoleName: "~",
		RoleId:   "~",
	},
}

// getUserPasswordCredential 从数据库获取用户密码凭证
func getUserPasswordCredential(qid int64) (*models.UserCredentialDBItem, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT qid, crdt_type, crdt_key, crdt_value, created_at, expire_at, status, extra
		FROM user_credentials
		WHERE qid = $1 AND crdt_type = $2 AND status = $3 AND expire_at > $4
	`

	now := time.Now().UnixMilli()
	credentialInfo := models.UserCredentialDBItem{}
	err := db.QueryRow(query, qid, models.CredentialTypePassword, models.UserCredentialStatusNormal, now).Scan(&credentialInfo.Qid, &credentialInfo.CrdtType, &credentialInfo.CrdtKey, &credentialInfo.CrdtValue, &credentialInfo.CreatedAt, &credentialInfo.ExpireAt, &credentialInfo.Status, &credentialInfo.Extra)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // 密码不存在
		}
		return nil, err
	}

	return &credentialInfo, nil
}

// findUserByPhone 通过手机号查找用户
func findUserByPhone(phone string) (*models.UserBasicInfoDBItem, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT qid, phone, custom_qid, username, avatar_id, role, has_initialized
		FROM user_basic_profiles
		WHERE phone = $1
	`

	userinfo := models.UserBasicInfoDBItem{}
	err := db.QueryRow(query, phone).Scan(&userinfo.Qid, &userinfo.Phone, &userinfo.CustomQid, &userinfo.Username, &userinfo.AvatarId, &userinfo.Role, &userinfo.HasInitialized)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // 用户不存在
		}
		return nil, err
	}

	return &userinfo, nil
}

// findUserByQid 通过qid查找用户
func findUserByQid(qid int64) (*models.UserBasicInfoDBItem, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT qid, phone, custom_qid, username, avatar_id, role, has_initialized
		FROM user_basic_profiles
		WHERE qid = $1
	`

	userinfo := models.UserBasicInfoDBItem{}
	err := db.QueryRow(query, qid).Scan(&userinfo.Qid, &userinfo.Phone, &userinfo.CustomQid, &userinfo.Username, &userinfo.AvatarId, &userinfo.Role, &userinfo.HasInitialized)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // 用户不存在
		}
		return nil, err
	}

	return &userinfo, nil
}

// getUserExtraProfile 获取用户额外资料
func getUserExtraProfile(qid int64) (*models.UserExtraProfileDBItem, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT qid, bio, birthday, birthday_blur, zodiac_sign, height, height_blur, weight, weight_blur, current_city, home_town, mbti
		FROM user_profiles_extra
		WHERE qid = $1
	`

	userinfo := models.UserExtraProfileDBItem{}
	err := db.QueryRow(query, qid).Scan(&userinfo.Qid, &userinfo.Bio, &userinfo.Birthday, &userinfo.BirthdayBlur, &userinfo.ZodiacSign, &userinfo.Height, &userinfo.HeightBlur, &userinfo.Weight, &userinfo.WeightBlur, &userinfo.CurrentCity, &userinfo.HomeTown, &userinfo.MBTI)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // 用户不存在
		}
		return nil, err
	}

	return &userinfo, nil
}

func roleIdToFloat(roleId string) float32 {
	return 0
}

func roleIdToRoleName(roleId string) string {
	return "0"
}

func floatToRoleId(role float32) string {
	return ""
}

// intRoleToRoleId 将int角色值转换为字符串角色名
func intRoleToRoleId(intRole int) string {
	// -1:不选 0 1 2 3 4 5 6 7 8 9 10, 11:side 12:~
	if intRole >= 1 && intRole <= 9 {
		return fmt.Sprintf("%.1d", intRole/10.0)
	}
	if intRole == 0 {
		return "0"
	}
	if intRole == 10 {
		return "1"
	}
	if intRole == -1 {
		return "none"
	}
	if intRole == 11 {
		return "side"
	}
	if intRole == 12 {
		return "~"
	}
	return "none"
}

func getRoleConfig(roleId string) *UserRole {
	for _, role := range qingUserRoles {
		if role.RoleId == roleId {
			return &role
		}
	}
	return nil
}

// generateAvatarInfo 生成头像的信息
func generateAvatarObjectKey(prefix, suffix string) string {
	randomId := ""
	for i := 0; i < 10; i++ {
		id, err := gonanoid.Generate("0123456789abcdefghijklmnopqrstuvwxyz", 14)
		if err == nil {
			randomId = id
			break
		}
	}

	if randomId == "" {
		randomId = utils.GetRandomString(14)
	}

	ms := time.Now().UnixMilli()
	if suffix == "" {
		return fmt.Sprintf("avatar/%v-%v-%v", prefix, ms, randomId)
	}

	return fmt.Sprintf("avatar/%v-%v-%v.%v", prefix, ms, randomId, suffix)
}

func generateNewUserName() string {
	id, err := gonanoid.Generate("123456789abcdefghijklmnopqrstuvwxyz", 9)
	if err != nil {
		id = utils.GetRandomString(9)
	}
	return "用户" + id
}

// generateFeedMediaObjectKey 生成动态媒体的objectKey
func generateFeedMediaObjectKey(prefix, suffix string) string {
	randomId := ""
	for i := 0; i < 10; i++ {
		id, err := gonanoid.Generate("0123456789abcdefghijklmnopqrstuvwxyz", 14)
		if err == nil {
			randomId = id
			break
		}
	}

	if randomId == "" {
		randomId = utils.GetRandomString(14)
	}

	ms := time.Now().UnixMilli()
	if suffix == "" {
		return fmt.Sprintf("feed/%v-%v-%v", prefix, ms, randomId)
	}

	return fmt.Sprintf("feed/%v-%v-%v.%v", prefix, ms, randomId, suffix)
}

// getUploadAvatarInfo 获取头像上传信息
func getUploadMediaInfo(mediaId int64, qid int64) (*models.UserGenMediasDBItem, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	item := models.UserGenMediasDBItem{}
	query := `
		SELECT media_id, qid, scenario, media_type, media_object_key, file_size, status, created_at, thumbhash, width, height, duration, video_cover, exif, ocr, ai_desc, extra
		FROM user_gen_medias
		WHERE media_id = $1 AND qid = $2`
	err := db.QueryRow(query, mediaId, qid).Scan(
		&item.MediaId,
		&item.Qid,
		&item.Scenario,
		&item.MediaType,
		&item.MediaObjectKey,
		&item.FileSize,
		&item.Status,
		&item.CreatedAt,
		&item.ThumbHash,
		&item.Width,
		&item.Height,
		&item.Duration,
		&item.VideoCover,
		&item.Exif,
		&item.OCR,
		&item.AiDesc,
		&item.Extra,
	)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	return &item, nil
}

// insertUserProfilePendingReviewTask 插入用户资料审核记录
func insertUserProfilePendingReviewTask(qid int64, scenario int, content string, extra any) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	now := time.Now().UnixMilli()
	taskId := utils.GeneratePendingReviewTaskID()
	status := models.PendingReviewFeedStatusPending // 状态 0:待审核 1:审核通过 -1:审核不通过 -2:已取消(比如新待审头像覆盖了旧待审头像)

	insertQuery := `
			INSERT INTO pending_review_profiles (task_id, qid, scenario, content, created_at, status, extra)
			VALUES ($1, $2, $3, $4, $5, $6, $7)
		`
	_, err := db.Exec(insertQuery, taskId, qid, scenario, content, now, status, extra)
	if err != nil {
		return err
	}
	return nil
}

type PendingReviewAvatarExtra struct {
	MediaId int64 `json:"mediaId"`
}

type UserGenMediaExtra struct {
	DeviceId string `json:"device_id,omitempty"`
}
