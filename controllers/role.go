package controllers

import (
	"errors"
	"qing-profiles/utils"
	"strconv"

	"github.com/kataras/iris/v12"
)

type GetRolesResponse struct {
	List []UserRole `json:"list"`
}

type UserRole struct {
	RoleName string `json:"role_name"`
	RoleId   string `json:"role_id"`
}

// GetRoles 处理 POST /api/users/profile/roles
// 获取角色列表
func (p *ProfileController) GetRoles(ctx iris.Context) {
	response := GetRolesResponse{
		List: qingUserRoles,
	}
	// 返回成功响应
	utils.SuccessWithMsg(ctx, response, "")
}

// toIntRole 将字符串角色转换为有效的int角色值 side:11 ~:12 不选:-1
func (p *ProfileController) toIntRole(role string) (int, error) {
	switch role {
	case "0", "0.1", "0.2", "0.3", "0.4", "0.5", "0.6", "0.7", "0.8", "0.9", "1":
		fRole, _ := strconv.ParseFloat(role, 32)
		return int(fRole * 10), nil
	case "none":
		return -1, nil
	case "side":
		return 11, nil
	case "~":
		return 12, nil
	}
	return 0, errors.New("不支持的角色类型")
}
