package controllers

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"maps"
	"qing-profiles/database"
	"qing-profiles/models"
	"qing-profiles/utils"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/kataras/iris/v12"
)

type UpdateUserBirthdayRequest struct {
	Birthday string `json:"birthday"` // [yyyy-mm-dd]
	Blur     bool   `json:"blur"`
}

// UpdateUserBirthday
// 修改用户生日
func (p *ProfileController) UpdateUserBirthday(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserBirthdayRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserBirthday 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.Birthday = strings.TrimSpace(req.Birthday)
	if req.Birthday == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 把birthday 按照 yyyy-mm-dd 解析成年月日
	dateParts := strings.Split(req.Birthday, "-")
	if len(dateParts) != 3 {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 把生日转化为时间戳
	birthdayTime, err := time.Parse("2006-01-02", req.Birthday)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"birthday": req.Birthday,
		}).Error("UpdateUserBirthday 解析生日失败")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// TODO 判断一下年月日的合法性

	// 保存到数据库
	birthday := birthdayTime.Unix()
	err = p.saveUserBirthday(qid, birthday, req.Blur)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"birthday": req.Birthday,
		}).Error("UpdateUserBirthday 保存生日失败")
		utils.InternalError(ctx, "")
		return
	}

	// 保存到缓存
	err = p.saveUserProfileToCache(qid, map[string]string{
		"birthday":      fmt.Sprint(birthday),
		"birthday_blur": fmt.Sprint(req.Blur),
	})
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"birthday": birthday,
		}).Error("saveUserProfileToCache 保存到缓存失败")
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserZodiacSignRequest struct {
	ZodiacSign string `json:"zodiac_sign"`
}

// UpdateUserZodiacSign
// 修改用户星座
func (p *ProfileController) UpdateUserZodiacSign(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserZodiacSignRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserZodiacSign 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.ZodiacSign = strings.TrimSpace(req.ZodiacSign)
	if req.ZodiacSign == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 转换星座为整型
	zodiacSignInt, err := toIntZodiacSign(req.ZodiacSign)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":         qid,
			"zodiac_sign": req.ZodiacSign,
		}).Error("UpdateUserZodiacSign 星座类型转换失败")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 保存到数据库
	err = p.saveUserZodiacSign(qid, zodiacSignInt)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":         qid,
			"zodiac_sign": req.ZodiacSign,
		}).Error("UpdateUserZodiacSign 保存星座失败")
		utils.InternalError(ctx, "")
		return
	}

	// 保存到缓存
	err = p.saveUserProfileToCache(qid, map[string]string{
		"zodiac_sign": fmt.Sprint(zodiacSignInt),
	})
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":         qid,
			"zodiac_sign": zodiacSignInt,
		}).Error("saveUserProfileToCache 保存到缓存失败")
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserHeightRequest struct {
	Height string `json:"height"`
	Blur   bool   `json:"blur"`
}

// UpdateUserHeight
// 修改用户身高
func (p *ProfileController) UpdateUserHeight(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserHeightRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserHeight 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.Height = strings.TrimSpace(req.Height)
	if req.Height == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 转换身高为整型
	var heightInt int
	var err error

	if req.Height == "none" {
		heightInt = -1
	} else {
		heightInt, err = strconv.Atoi(req.Height)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":    qid,
				"height": req.Height,
			}).Error("UpdateUserHeight 身高转换失败")
			utils.InvalidRequestError(ctx, "")
			return
		}
	}

	// 保存到数据库
	err = p.saveUserHeight(qid, heightInt, req.Blur)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":    qid,
			"height": req.Height,
		}).Error("UpdateUserHeight 保存身高失败")
		utils.InternalError(ctx, "")
		return
	}

	// 保存到缓存
	err = p.saveUserProfileToCache(qid, map[string]string{
		"height":      fmt.Sprint(heightInt),
		"height_blur": fmt.Sprint(req.Blur),
	})
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":    qid,
			"height": heightInt,
			"blur":   req.Blur,
		}).Error("saveUserProfileToCache 保存到缓存失败")
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserWeightRequest struct {
	Weight string `json:"weight"`
	Blur   bool   `json:"blur"`
}

// UpdateUserWeight
// 修改用户体重
func (p *ProfileController) UpdateUserWeight(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserWeightRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserWeight 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.Weight = strings.TrimSpace(req.Weight)
	if req.Weight == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 转换体重为整型
	var weightInt int
	var err error

	if req.Weight == "none" {
		weightInt = -1
	} else {
		weightInt, err = strconv.Atoi(req.Weight)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":    qid,
				"weight": req.Weight,
			}).Error("UpdateUserWeight 体重转换失败")
			utils.InvalidRequestError(ctx, "")
			return
		}
	}

	// 保存到数据库
	err = p.saveUserWeight(qid, weightInt, req.Blur)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":    qid,
			"weight": req.Weight,
		}).Error("UpdateUserWeight 保存体重失败")
		utils.InternalError(ctx, "")
		return
	}

	// 保存到缓存
	err = p.saveUserProfileToCache(qid, map[string]string{
		"weight":      fmt.Sprint(weightInt),
		"weight_blur": fmt.Sprint(req.Blur),
	})
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":    qid,
			"weight": weightInt,
			"blur":   req.Blur,
		}).Error("saveUserProfileToCache 保存到缓存失败")
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserRoleRequest struct {
	Role string `json:"role"`
}

// UpdateUserRole
// 修改用户角色
func (p *ProfileController) UpdateUserRole(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserRoleRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserRole 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.Role = strings.TrimSpace(req.Role)
	if req.Role == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 转换角色为整型
	roleInt, err := p.toIntRole(req.Role)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":  qid,
			"role": req.Role,
		}).Error("UpdateUserRole 角色转换失败")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 保存到数据库
	err = p.saveUserRole(qid, roleInt)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":  qid,
			"role": req.Role,
		}).Error("UpdateUserRole 保存角色失败")
		utils.InternalError(ctx, "")
		return
	}

	// 保存到缓存
	err = p.saveUserProfileToCache(qid, map[string]string{
		"role": fmt.Sprint(roleInt),
	})
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":  qid,
			"role": roleInt,
		}).Error("saveUserProfileToCache 保存到缓存失败")
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserRegularCityRequest struct {
	RegularCity string `json:"regular_city"`
}

// UpdateUserRegularCity
// 修改用户常居地
func (p *ProfileController) UpdateUserRegularCity(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserRegularCityRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserRegularCity 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.RegularCity = strings.TrimSpace(req.RegularCity)
	if req.RegularCity == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 保存到数据库
	err := p.saveUserRegularCity(qid, req.RegularCity)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":          qid,
			"regular_city": req.RegularCity,
		}).Error("UpdateUserRegularCity 保存常居地失败")
		utils.InternalError(ctx, "")
		return
	}

	// 保存到缓存
	err = p.saveUserProfileToCache(qid, map[string]string{
		"regular_city": req.RegularCity,
	})
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":          qid,
			"regular_city": req.RegularCity,
		}).Error("saveUserProfileToCache 保存到缓存失败")
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserHometownRequest struct {
	Hometown string `json:"hometown"`
}

// UpdateUserHometown
// 修改用户家乡
func (p *ProfileController) UpdateUserHometown(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserHometownRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserHometown 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.Hometown = strings.TrimSpace(req.Hometown)
	if req.Hometown == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 保存到数据库
	err := p.saveUserHometown(qid, req.Hometown)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"hometown": req.Hometown,
		}).Error("UpdateUserHometown 保存家乡失败")
		utils.InternalError(ctx, "")
		return
	}

	// 保存到缓存
	err = p.saveUserProfileToCache(qid, map[string]string{
		"hometown": req.Hometown,
	})
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"hometown": req.Hometown,
		}).Error("saveUserProfileToCache 保存到缓存失败")
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserMBTIRequest struct {
	MBTI string `json:"mbti"`
}

// UpdateUserMBTI
// 修改用户mbti
func (p *ProfileController) UpdateUserMBTI(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserMBTIRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserMBTI 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.MBTI = strings.TrimSpace(req.MBTI)
	if req.MBTI == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 转换MBTI为整型
	mbtiInt, err := p.toIntMBTI(req.MBTI)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":  qid,
			"mbti": req.MBTI,
		}).Error("UpdateUserMBTI MBTI转换失败")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 保存到数据库
	err = p.saveUserMBTI(qid, mbtiInt)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":  qid,
			"mbti": req.MBTI,
		}).Error("UpdateUserMBTI 保存MBTI失败")
		utils.InternalError(ctx, "")
		return
	}

	// 保存到缓存
	err = p.saveUserProfileToCache(qid, map[string]string{
		"mbti": req.MBTI,
	})
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":  qid,
			"mbti": req.MBTI,
		}).Error("saveUserProfileToCache 保存到缓存失败")
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

// 保存用户生日
func (p *ProfileController) saveUserBirthday(qid, birthday int64, blur bool) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_profiles_extra
		SET birthday = $1, birthday_blur = $2
		WHERE qid = $3
	`

	_, err := db.Exec(query, birthday, blur, qid)
	if err != nil {
		return err
	}
	return nil
}

// 保存用户星座
func (p *ProfileController) saveUserZodiacSign(qid int64, zodiacSign int) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_profiles_extra
		SET zodiac_sign = $1
		WHERE qid = $2
	`

	_, err := db.Exec(query, zodiacSign, qid)
	if err != nil {
		return err
	}
	return nil
}

// 保存用户身高
func (p *ProfileController) saveUserHeight(qid int64, height int, blur bool) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_profiles_extra
		SET height = $1, height_blur = $2
		WHERE qid = $3
	`

	_, err := db.Exec(query, height, blur, qid)
	if err != nil {
		return err
	}
	return nil
}

// 保存用户体重
func (p *ProfileController) saveUserWeight(qid int64, weight int, blur bool) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_profiles_extra
		SET weight = $1, weight_blur = $2
		WHERE qid = $3
	`

	_, err := db.Exec(query, weight, blur, qid)
	if err != nil {
		return err
	}
	return nil
}

// 保存用户角色
func (p *ProfileController) saveUserRole(qid int64, role int) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_profiles_extra
		SET role = $1
		WHERE qid = $2
	`

	_, err := db.Exec(query, role, qid)
	if err != nil {
		return err
	}
	return nil
}

// 保存用户常居地
func (p *ProfileController) saveUserRegularCity(qid int64, regularCity string) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_profiles_extra
		SET regular_city = $1
		WHERE qid = $2
	`

	_, err := db.Exec(query, regularCity, qid)
	if err != nil {
		return err
	}
	return nil
}

// 保存用户家乡
func (p *ProfileController) saveUserHometown(qid int64, hometown string) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_profiles_extra
		SET hometown = $1
		WHERE qid = $2
	`

	_, err := db.Exec(query, hometown, qid)
	if err != nil {
		return err
	}
	return nil
}

// 保存用户MBTI
func (p *ProfileController) saveUserMBTI(qid int64, mbti int) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_profiles_extra
		SET mbti = $1
		WHERE qid = $2
	`

	_, err := db.Exec(query, mbti, qid)
	if err != nil {
		return err
	}
	return nil
}

type UpdateUsernameRequest struct {
	Username string `json:"username"`
}

// UpdateUsername
// 修改用户昵称
func (p *ProfileController) UpdateUsername(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUsernameRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUsername 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.Username = strings.TrimSpace(req.Username)
	if req.Username == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// TODO 需要对 req.Username 进行机器审核
	scenario := models.PendingReviewProfileScenarioUpdateUsername // 场景 1:修改qingID 2:修改头像 3:修改昵称 4:修改简介 5:修改个人主页封面 6:修改好友圈封面
	err := insertUserProfilePendingReviewTask(qid, scenario, req.Username, nil)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"username": req.Username,
		}).Error("UpdateUsername 插入审核表失败")
		utils.InternalError(ctx, "")
		return
	}

	// 保存到缓存
	err = p.saveUserProfileToCache(qid, map[string]string{
		"pending:username": req.Username,
	})
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"username": req.Username,
		}).Error("UpdateUsername 保存到缓存失败")
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserAvatarRequest struct {
	MediaId string `json:"media_id"`
}

// UpdateUserAvatar
// 修改用户头像
func (p *ProfileController) UpdateUserAvatar(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserAvatarRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserAvatar 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.MediaId = strings.TrimSpace(req.MediaId)
	if req.MediaId == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	mediaId, err := utils.DecodeMediaID(req.MediaId)
	if err != nil || mediaId <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":     qid,
			"mediaId": req.MediaId,
		}).Error("UpdateUserAvatar DecodeMediaID 失败")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 获取媒体信息
	userGenMedia, err := getUploadMediaInfo(mediaId, qid)
	if err != nil || userGenMedia == nil || userGenMedia.MediaObjectKey == "" {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"media_id": req.MediaId,
		}).Error("UpdateUserAvatar getUploadMediaInfo 失败")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 场景 1:设置头像 2:发布动态
	// 状态 -2:已删除 -1:未通过审核 0:默认 1:通过机审 2:已使用 3:已审核
	if userGenMedia.Qid != qid || userGenMedia.Scenario != 1 || userGenMedia.Status != 1 {
		logrus.WithFields(logrus.Fields{
			"qid":      qid,
			"media_id": req.MediaId,
		}).Error("UpdateUserAvatar 媒体资源不符合预期")
		utils.InvalidRequestError(ctx, "")
		return
	}

	var extra any
	if extraBytes, mErr := json.Marshal(&PendingReviewAvatarExtra{
		MediaId: mediaId,
	}); mErr == nil {
		extra = string(extraBytes)
	}

	scenario := models.PendingReviewProfileScenarioUpdateAvatar // 场景 1:修改qingID 2:修改头像 3:修改昵称 4:修改简介 5:修改个人主页封面 6:修改好友圈封面
	err = insertUserProfilePendingReviewTask(qid, scenario, userGenMedia.MediaObjectKey, extra)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid": qid,
		}).Error("UpdateUserAvatar insertUserProfilePendingReviewTask 插入审核表失败")
		utils.InternalError(ctx, "")
		return
	}

	// 保存到缓存
	err = p.saveUserProfileToCache(qid, map[string]string{
		"pending:avatar_object_key": userGenMedia.MediaObjectKey,
		"pending:avatar_media_id":   fmt.Sprint(mediaId),
	})
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":    qid,
			"avatar": userGenMedia.MediaObjectKey,
		}).Error("UpdateUserAvatar 保存到缓存失败")
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserQingIdRequest struct {
	QingId string `json:"qing_id"`
}

// UpdateUserQingId
// 修改用户qing id
func (p *ProfileController) UpdateUserQingId(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserQingIdRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserQingId 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.QingId = strings.TrimSpace(req.QingId)
	if req.QingId == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// TODO 需要对 req.QingId 进行机器审核

	scenario := models.PendingReviewProfileScenarioUpdateQingID // 场景 1:修改qingID 2:修改头像 3:修改昵称 4:修改简介 5:修改个人主页封面 6:修改好友圈封面
	err := insertUserProfilePendingReviewTask(qid, scenario, req.QingId, nil)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":        qid,
			"custom_qid": req.QingId,
		}).Error("UpdateUserQingId 插入审核表失败")
		utils.InternalError(ctx, "")
		return
	}

	// 保存到缓存
	err = p.saveUserProfileToCache(qid, map[string]string{
		"pending:custom_qid": req.QingId,
	})
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":        qid,
			"custom_qid": req.QingId,
		}).Error("saveUserProfileToCache 保存到缓存失败")
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserBioRequest struct {
	Bio string `json:"bio"` // biography 通常指的是用户的个人简介或自我介绍
}

// UpdateUserBio
// 修改用户签名
func (p *ProfileController) UpdateUserBio(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserBioRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserBio 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.Bio = strings.TrimSpace(req.Bio)
	if req.Bio == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// TODO 需要对 req.Bio 进行机器审核
	scenario := models.PendingReviewProfileScenarioUpdateBio // 场景 1:修改qingID 2:修改头像 3:修改昵称 4:修改简介 5:修改个人主页封面 6:修改好友圈封面
	err := insertUserProfilePendingReviewTask(qid, scenario, req.Bio, nil)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid": qid,
			"bio": req.Bio,
		}).Error("UpdateUserBio 插入审核表失败")
		utils.InternalError(ctx, "")
		return
	}

	// 保存到缓存
	err = p.saveUserProfileToCache(qid, map[string]string{
		"pending:bio": req.Bio,
	})
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid": qid,
			"bio": req.Bio,
		}).Error("saveUserProfileToCache 保存到缓存失败")
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserTagsRequest struct {
	Tags []string `json:"tags"`
}

// UpdateUserPersonalities
// 修改用户签名
func (p *ProfileController) UpdateUserPersonalities(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserTagsRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserPersonalities 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	intTags := make([]int, 0)
	strTags := make([]string, 0)
	for _, tag := range req.Tags {
		tag = strings.TrimSpace(tag)
		if tag == "" {
			continue
		}
		intTag, err := strconv.Atoi(tag)
		if err != nil {
			continue
		}
		intTags = append(intTags, intTag)
		strTags = append(strTags, tag)
	}

	tagType := models.UserProfileTagTypePersonality // 1:个性标签 2:兴趣标签 3:情感标签 4:未来预期标签
	err := p.saveUserProfilesTags(qid, tagType, intTags)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":  qid,
			"tags": req.Tags,
		}).Error("saveUserProfilesTags 保存到数据库失败")
		utils.InternalError(ctx, "")
		return
	}

	{
		// 保存到缓存
		newTagsStr := strings.Join(strTags, "|")
		err = p.saveUserProfileToCache(qid, map[string]string{
			"personalities:tags": newTagsStr,
		})
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":  qid,
				"tags": req.Tags,
			}).Error("saveUserProfileToCache 保存到缓存失败")
		}
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

// 保存用户标签
func (p *ProfileController) saveUserProfilesTags(qid int64, tagType int, newTags []int) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	tx, err := db.Begin()
	if err != nil {
		return err
	}

	_, err = tx.Exec(`DELETE FROM user_profiles_tags WHERE qid = $1 AND tag_type = $2`, qid, tagType)
	if err != nil {
		_ = tx.Rollback()
		return err
	}

	// 3. 如果有新标签需要设置，则批量插入它们
	if len(newTags) > 0 {
		// 安全地构建批量 INSERT 语句
		insertQuery, insertArgs := buildInsertStatement(qid, tagType, newTags)
		_, err = tx.Exec(insertQuery, insertArgs...)
		if err != nil {
			_ = tx.Rollback()
			return err
		}
	}

	if err = tx.Commit(); err != nil {
		_ = tx.Rollback()
		return err
	}
	return nil
}

// UpdateUserInterests
// 修改用户兴趣
func (p *ProfileController) UpdateUserInterests(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserTagsRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserInterests 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	intTags := make([]int, 0)
	strTags := make([]string, 0)
	for _, tag := range req.Tags {
		tag = strings.TrimSpace(tag)
		if tag == "" {
			continue
		}
		intTag, err := strconv.Atoi(tag)
		if err != nil {
			continue
		}
		intTags = append(intTags, intTag)
		strTags = append(strTags, tag)
	}

	tagType := models.UserProfileTagTagTypeInterest // 1:个性标签 2:兴趣标签 3:情感标签 4:未来预期标签
	err := p.saveUserProfilesTags(qid, tagType, intTags)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":  qid,
			"tags": req.Tags,
		}).Error("saveUserProfilesTags 保存到数据库失败")
		utils.InternalError(ctx, "")
		return
	}

	{
		// 保存到缓存
		newTagsStr := strings.Join(strTags, "|")
		err = p.saveUserProfileToCache(qid, map[string]string{
			"interests:tags": newTagsStr,
		})
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":  qid,
				"tags": req.Tags,
			}).Error("saveUserProfileToCache 保存到缓存失败")
		}
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

// UpdateUserRelationshipStatus
// 修改用户情感状态
func (p *ProfileController) UpdateUserRelationshipStatus(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserTagsRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserRelationshipStatus 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	intTags := make([]int, 0)
	strTags := make([]string, 0)
	for _, tag := range req.Tags {
		tag = strings.TrimSpace(tag)
		if tag == "" {
			continue
		}
		intTag, err := strconv.Atoi(tag)
		if err != nil {
			continue
		}
		intTags = append(intTags, intTag)
		strTags = append(strTags, tag)
	}

	tagType := models.UserProfileTagTagTypeRelationshipStatus // 1:个性标签 2:兴趣标签 3:情感标签 4:未来预期标签
	err := p.saveUserProfilesTags(qid, tagType, intTags)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":  qid,
			"tags": req.Tags,
		}).Error("saveUserProfilesTags 保存到数据库失败")
		utils.InternalError(ctx, "")
		return
	}

	{
		// 保存到缓存
		newTagsStr := strings.Join(strTags, "|")
		err = p.saveUserProfileToCache(qid, map[string]string{
			"relationship_status:tags": newTagsStr,
		})
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":  qid,
				"tags": req.Tags,
			}).Error("saveUserProfileToCache 保存到缓存失败")
		}
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

// UpdateUserFutureGoals
// 修改用户未来预期
func (p *ProfileController) UpdateUserFutureGoals(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserTagsRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserRelationshipStatus 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	intTags := make([]int, 0)
	strTags := make([]string, 0)
	for _, tag := range req.Tags {
		tag = strings.TrimSpace(tag)
		if tag == "" {
			continue
		}
		intTag, err := strconv.Atoi(tag)
		if err != nil {
			continue
		}
		intTags = append(intTags, intTag)
		strTags = append(strTags, tag)
	}

	tagType := models.UserProfileTagTagTypeFutureGoal // 1:个性标签 2:兴趣标签 3:情感标签 4:未来预期标签
	err := p.saveUserProfilesTags(qid, tagType, intTags)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":  qid,
			"tags": req.Tags,
		}).Error("saveUserProfilesTags 保存到数据库失败")
		utils.InternalError(ctx, "")
		return
	}

	{
		// 保存到缓存
		newTagsStr := strings.Join(strTags, "|")
		err = p.saveUserProfileToCache(qid, map[string]string{
			"future_goals:tags": newTagsStr,
		})
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":  qid,
				"tags": req.Tags,
			}).Error("saveUserProfileToCache 保存到缓存失败")
		}
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

// buildInsertStatement 为批量 INSERT 安全地构建查询语句和参数列表
// 这是一个可复用的辅助函数
func buildInsertStatement(qid int64, tagType int, tagsToAdd []int) (string, []interface{}) {
	var valueStrings []string
	var args []interface{}

	now := time.Now().UnixMilli()
	argCounter := 1
	for _, tagID := range tagsToAdd {
		// 每个插入组是 (userID, tagID, tagType, created_at)
		// 占位符格式为 ($1, $2, $3, $4), ($5, $6, $7, $8), ...
		valueStrings = append(valueStrings, fmt.Sprintf("($%d, $%d, $%d, $%d)", argCounter, argCounter+1, argCounter+2, argCounter+3))
		args = append(args, qid, tagID, tagType, now)
		argCounter += 4
	}

	query := fmt.Sprintf(
		"INSERT INTO user_profiles_tags (qid, tag_id, tag_type, created_at) VALUES %s;",
		strings.Join(valueStrings, ","),
	)

	return query, args
}

// UpdateUserX
// 修改用户x
func (p *ProfileController) UpdateUserX(ctx iris.Context) {
	//qid, _ := ctx.Values().GetInt64("x-qid")

	utils.SuccessWithMsg(ctx, nil, "")
	return
}

type GetUserQingIdUpdateQuotaResponse struct {
	Quota int `json:"quota"`
}

// GetUserQingIdUpdateQuota
// 用户qing id 的可修改次数
func (p *ProfileController) GetUserQingIdUpdateQuota(ctx iris.Context) {
	//qid, _ := ctx.Values().GetInt64("x-qid")
	response := GetUserQingIdUpdateQuotaResponse{
		Quota: 1,
	}
	utils.SuccessWithMsg(ctx, response, "")
	return
}

type GetUsernameUpdateQuotaResponse struct {
	Quota int `json:"quota"`
}

// GetUserUsernameUpdateQuota
// 用户username 的可修改次数
func (p *ProfileController) GetUserUsernameUpdateQuota(ctx iris.Context) {
	//qid, _ := ctx.Values().GetInt64("x-qid")
	response := GetUsernameUpdateQuotaResponse{
		Quota: 1,
	}
	utils.SuccessWithMsg(ctx, response, "")
	return
}

// toIntZodiacSign 将字符串星座转换为有效的int星座值
func toIntZodiacSign(zodiacSign string) (int, error) {
	switch zodiacSign {
	case "aries":
		return models.ZodiacSignAries, nil
	case "taurus":
		return models.ZodiacSignTaurus, nil
	case "gemini":
		return models.ZodiacSignGemini, nil
	case "cancer":
		return models.ZodiacSignCancer, nil
	case "leo":
		return models.ZodiacSignLeo, nil
	case "virgo":
		return models.ZodiacSignVirgo, nil
	case "libra":
		return models.ZodiacSignLibra, nil
	case "scorpio":
		return models.ZodiacSignScorpio, nil
	case "sagittarius":
		return models.ZodiacSignSagittarius, nil
	case "capricorn":
		return models.ZodiacSignCapricorn, nil
	case "aquarius":
		return models.ZodiacSignAquarius, nil
	case "pisces":
		return models.ZodiacSignPisces, nil
	case "none":
		return models.ZodiacSignNone, nil
	}
	return 0, errors.New("不支持的星座类型")
}

// toStringZodiacSign 将int星座转换为有效的字符串星座值
func toStringZodiacSign(zodiacSign int) (string, error) {
	switch zodiacSign {
	case models.ZodiacSignAries:
		return "aries", nil
	case models.ZodiacSignTaurus:
		return "taurus", nil
	case models.ZodiacSignGemini:
		return "gemini", nil
	case models.ZodiacSignCancer:
		return "cancer", nil
	case models.ZodiacSignLeo:
		return "leo", nil
	case models.ZodiacSignVirgo:
		return "virgo", nil
	case models.ZodiacSignLibra:
		return "libra", nil
	case models.ZodiacSignScorpio:
		return "scorpio", nil
	case models.ZodiacSignSagittarius:
		return "sagittarius", nil
	case models.ZodiacSignCapricorn:
		return "capricorn", nil
	case models.ZodiacSignAquarius:
		return "aquarius", nil
	case models.ZodiacSignPisces:
		return "pisces", nil
	case models.ZodiacSignNone:
		return "none", nil
	}
	return "", errors.New("不支持的星座类型")
}

// toIntMBTI 将字符串MBTI转换为有效的int MBTI值
func (p *ProfileController) toIntMBTI(mbti string) (int, error) {
	mbtiMap := map[string]int{
		"intj": 1, "intp": 2, "entj": 3, "entp": 4,
		"infj": 5, "infp": 6, "enfj": 7, "enfp": 8,
		"istj": 9, "isfj": 10, "estj": 11, "esfj": 12,
		"istp": 13, "isfp": 14, "estp": 15, "esfp": 16,
		"none": -1,
	}

	if value, exists := mbtiMap[strings.ToLower(mbti)]; exists {
		return value, nil
	}
	return 0, errors.New("不支持的MBTI类型")
}

// 保存用户资料到缓存
func (p *ProfileController) saveUserProfileToCache(qid int64, profiles map[string]string) error {
	if len(profiles) <= 0 {
		return nil
	}

	client := database.GetRedisClient()
	if client == nil {
		return errors.New("redis client is nil")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	key := fmt.Sprintf("user:%v:profiles", qid)

	err := client.Do(ctx, client.B().Hmset().Key(key).FieldValue().FieldValueIter(maps.All(profiles)).Build()).Error()
	if err != nil {
		return err
	}
	return nil
}

func (p *ProfileController) getUserProfileCache(qid int64) (map[string]string, error) {
	client := database.GetRedisClient()
	if client == nil {
		return nil, errors.New("redis client is nil")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	key := fmt.Sprintf("user:%v:profiles", qid)

	valueMap, err := client.DoCache(ctx, client.B().Hgetall().Key(key).Cache(), time.Hour).AsStrMap()
	if err != nil {
		return nil, err
	}

	return valueMap, nil
}
