package controllers

import (
	"database/sql"
	"encoding/json"
	"qing-profiles/database"
	"qing-profiles/utils"
	"strings"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/kataras/iris/v12"
)

type UpdateUserBirthdayRequest struct {
	Birthday string `json:"birthday"` // [yyyy-mm-dd]
	Blur     bool   `json:"blur"`
}

// UpdateUserBirthday
// 修改用户生日
func (p *ProfileController) UpdateUserBirthday(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserBirthdayRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserBirthday 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.Birthday = strings.TrimSpace(req.Birthday)
	if req.Birthday == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 把birthday 按照 yyyy-mm-dd 解析成年月日
	dateParts := strings.Split(req.Birthday, "-")
	if len(dateParts) != 3 {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 把生日转化为时间戳
	birthdayTime, err := time.Parse("2006-01-02", req.Birthday)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"birthday": req.Birthday,
		}).Error("UpdateUserBirthday 解析生日失败")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// TODO 判断一下年月日的合法性

	// 保存到数据库
	birthday := birthdayTime.Unix()
	err = p.saveUserBirthday(qid, birthday, req.Blur)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"birthday": req.Birthday,
		}).Error("UpdateUserBirthday 保存生日失败")
		utils.InternalError(ctx, "")
		return
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserZodiacSignRequest struct {
	ZodiacSign string `json:"zodiac_sign"`
}

// UpdateUserZodiacSign
// 修改用户星座
func (p *ProfileController) UpdateUserZodiacSign(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserZodiacSignRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserZodiacSign 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.ZodiacSign = strings.TrimSpace(req.ZodiacSign)
	if req.ZodiacSign == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 保存到数据库
	err := p.saveUserZodiacSign(qid, req.ZodiacSign)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":         qid,
			"zodiac_sign": req.ZodiacSign,
		}).Error("UpdateUserZodiacSign 保存星座失败")
		utils.InternalError(ctx, "")
		return
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserHeightRequest struct {
	Height string `json:"height"`
	Blur   bool   `json:"blur"`
}

// UpdateUserHeight
// 修改用户身高
func (p *ProfileController) UpdateUserHeight(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserHeightRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserHeight 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.Height = strings.TrimSpace(req.Height)
	if req.Height == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 保存到数据库
	err := p.saveUserHeight(qid, req.Height, req.Blur)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":    qid,
			"height": req.Height,
		}).Error("UpdateUserHeight 保存身高失败")
		utils.InternalError(ctx, "")
		return
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserWeightRequest struct {
	Weight string `json:"weight"`
	Blur   bool   `json:"blur"`
}

// UpdateUserWeight
// 修改用户体重
func (p *ProfileController) UpdateUserWeight(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserWeightRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserWeight 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.Weight = strings.TrimSpace(req.Weight)
	if req.Weight == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 保存到数据库
	err := p.saveUserWeight(qid, req.Weight, req.Blur)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":    qid,
			"weight": req.Weight,
		}).Error("UpdateUserWeight 保存体重失败")
		utils.InternalError(ctx, "")
		return
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserRoleRequest struct {
	Role string `json:"role"`
}

// UpdateUserRole
// 修改用户角色
func (p *ProfileController) UpdateUserRole(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserRoleRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserRole 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.Role = strings.TrimSpace(req.Role)
	if req.Role == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 保存到数据库
	err := p.saveUserRole(qid, req.Role)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":  qid,
			"role": req.Role,
		}).Error("UpdateUserRole 保存角色失败")
		utils.InternalError(ctx, "")
		return
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserRegularCityRequest struct {
	RegularCity string `json:"regular_city"`
}

// UpdateUserRegularCity
// 修改用户常居地
func (p *ProfileController) UpdateUserRegularCity(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserRegularCityRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserRegularCity 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.RegularCity = strings.TrimSpace(req.RegularCity)
	if req.RegularCity == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 保存到数据库
	err := p.saveUserRegularCity(qid, req.RegularCity)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":          qid,
			"regular_city": req.RegularCity,
		}).Error("UpdateUserRegularCity 保存常居地失败")
		utils.InternalError(ctx, "")
		return
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserHometownRequest struct {
	Hometown string `json:"hometown"`
}

// UpdateUserHometown
// 修改用户家乡
func (p *ProfileController) UpdateUserHometown(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserHometownRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserHometown 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.Hometown = strings.TrimSpace(req.Hometown)
	if req.Hometown == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 保存到数据库
	err := p.saveUserHometown(qid, req.Hometown)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"hometown": req.Hometown,
		}).Error("UpdateUserHometown 保存家乡失败")
		utils.InternalError(ctx, "")
		return
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserMBTIRequest struct {
	MBTI string `json:"mbti"`
}

// UpdateUserMBTI
// 修改用户mbti
func (p *ProfileController) UpdateUserMBTI(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserMBTIRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserMBTI 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.MBTI = strings.TrimSpace(req.MBTI)
	if req.MBTI == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 保存到数据库
	err := p.saveUserMBTI(qid, req.MBTI)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":  qid,
			"mbti": req.MBTI,
		}).Error("UpdateUserMBTI 保存MBTI失败")
		utils.InternalError(ctx, "")
		return
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

// 保存用户生日
func (p *ProfileController) saveUserBirthday(qid, birthday int64, blur bool) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_profiles_extra
		SET birthday = $1, birthday_blur = $2
		WHERE qid = $3
	`

	_, err := db.Exec(query, birthday, blur, qid)
	if err != nil {
		return err
	}
	return nil
}

// 保存用户星座
func (p *ProfileController) saveUserZodiacSign(qid int64, zodiacSign string) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_profiles_extra
		SET zodiac_sign = $1
		WHERE qid = $2
	`

	_, err := db.Exec(query, zodiacSign, qid)
	if err != nil {
		return err
	}
	return nil
}

// 保存用户身高
func (p *ProfileController) saveUserHeight(qid int64, height string, blur bool) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_profiles_extra
		SET height = $1, height_blur = $2
		WHERE qid = $3
	`

	_, err := db.Exec(query, height, blur, qid)
	if err != nil {
		return err
	}
	return nil
}

// 保存用户体重
func (p *ProfileController) saveUserWeight(qid int64, weight string, blur bool) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_profiles_extra
		SET weight = $1, weight_blur = $2
		WHERE qid = $3
	`

	_, err := db.Exec(query, weight, blur, qid)
	if err != nil {
		return err
	}
	return nil
}

// 保存用户角色
func (p *ProfileController) saveUserRole(qid int64, role string) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_profiles_extra
		SET role = $1
		WHERE qid = $2
	`

	_, err := db.Exec(query, role, qid)
	if err != nil {
		return err
	}
	return nil
}

// 保存用户常居地
func (p *ProfileController) saveUserRegularCity(qid int64, regularCity string) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_profiles_extra
		SET regular_city = $1
		WHERE qid = $2
	`

	_, err := db.Exec(query, regularCity, qid)
	if err != nil {
		return err
	}
	return nil
}

// 保存用户家乡
func (p *ProfileController) saveUserHometown(qid int64, hometown string) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_profiles_extra
		SET hometown = $1
		WHERE qid = $2
	`

	_, err := db.Exec(query, hometown, qid)
	if err != nil {
		return err
	}
	return nil
}

// 保存用户MBTI
func (p *ProfileController) saveUserMBTI(qid int64, mbti string) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_profiles_extra
		SET mbti = $1
		WHERE qid = $2
	`

	_, err := db.Exec(query, mbti, qid)
	if err != nil {
		return err
	}
	return nil
}

type UpdateUsernameRequest struct {
	Username string `json:"username"`
}

// UpdateUsername
// 修改用户昵称
func (p *ProfileController) UpdateUsername(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUsernameRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUsername 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.Username = strings.TrimSpace(req.Username)
	if req.Username == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// TODO 需要对 req.Username 进行机器审核
	scenario := 3 // 场景 1:修改qingID 2:修改头像 3:修改昵称 4:修改简介 5:修改个人主页封面 6:修改好友圈封面
	err := insertUserProfilePendingReviewTask(qid, scenario, req.Username, nil)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"username": req.Username,
		}).Error("UpdateUsername 插入审核表失败")
		utils.InternalError(ctx, "")
		return
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserAvatarRequest struct {
	MediaId string `json:"media_id"`
}

// UpdateUserAvatar
// 修改用户头像
func (p *ProfileController) UpdateUserAvatar(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserAvatarRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserAvatar 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.MediaId = strings.TrimSpace(req.MediaId)
	if req.MediaId == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	mediaId, err := utils.DecodeMediaID(req.MediaId)
	if err != nil || mediaId <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":     qid,
			"mediaId": req.MediaId,
		}).Error("UpdateUserAvatar DecodeMediaID 失败")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 获取媒体信息
	userGenMedia, err := getUploadMediaInfo(mediaId, qid)
	if err != nil || userGenMedia == nil || userGenMedia.MediaObjectKey == "" {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"media_id": req.MediaId,
		}).Error("UpdateUserAvatar getUploadMediaInfo 失败")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 场景 1:设置头像 2:发布动态
	// 状态 -2:已删除 -1:未通过审核 0:默认 1:通过机审 2:已使用 3:已审核
	if userGenMedia.Qid != qid || userGenMedia.Scenario != 1 || userGenMedia.Status != 1 {
		logrus.WithFields(logrus.Fields{
			"qid":      qid,
			"media_id": req.MediaId,
		}).Error("UpdateUserAvatar 媒体资源不符合预期")
		utils.InvalidRequestError(ctx, "")
		return
	}

	var extra any
	if extraBytes, mErr := json.Marshal(&PendingReviewAvatarExtra{
		MediaId: mediaId,
	}); mErr == nil {
		extra = string(extraBytes)
	}

	scenario := 2 // 场景 1:修改qingID 2:修改头像 3:修改昵称 4:修改简介 5:修改个人主页封面 6:修改好友圈封面
	err = insertUserProfilePendingReviewTask(qid, scenario, userGenMedia.MediaObjectKey, extra)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid": qid,
		}).Error("UpdateUserAvatar insertUserProfilePendingReviewTask 插入审核表失败")
		utils.InternalError(ctx, "")
		return
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserQingIdRequest struct {
	QingId string `json:"qing_id"`
}

// UpdateUserQingId
// 修改用户qing id
func (p *ProfileController) UpdateUserQingId(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserQingIdRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserQingId 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.QingId = strings.TrimSpace(req.QingId)
	if req.QingId == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// TODO 需要对 req.QingId 进行机器审核

	scenario := 1 // 场景 1:修改qingID 2:修改头像 3:修改昵称 4:修改简介 5:修改个人主页封面 6:修改好友圈封面
	err := insertUserProfilePendingReviewTask(qid, scenario, req.QingId, nil)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":        qid,
			"custom_qid": req.QingId,
		}).Error("UpdateUserQingId 插入审核表失败")
		utils.InternalError(ctx, "")
		return
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

type UpdateUserBioRequest struct {
	Bio string `json:"bio"` // biography 通常指的是用户的个人简介或自我介绍
}

// UpdateUserBio
// 修改用户签名
func (p *ProfileController) UpdateUserBio(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req UpdateUserBioRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateUserBio 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	req.Bio = strings.TrimSpace(req.Bio)
	if req.Bio == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// TODO 需要对 req.Bio 进行机器审核
	scenario := 4 // 场景 1:修改qingID 2:修改头像 3:修改昵称 4:修改简介 5:修改个人主页封面 6:修改好友圈封面
	err := insertUserProfilePendingReviewTask(qid, scenario, req.Bio, nil)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid": qid,
			"bio": req.Bio,
		}).Error("UpdateUserBio 插入审核表失败")
		utils.InternalError(ctx, "")
		return
	}

	utils.SuccessWithMsg(ctx, nil, "")
}

// UpdateUserX
// 修改用户x
func (p *ProfileController) UpdateUserX(ctx iris.Context) {
	//qid, _ := ctx.Values().GetInt64("x-qid")

	utils.SuccessWithMsg(ctx, nil, "")
	return
}

type GetUserQingIdUpdateQuotaResponse struct {
	Quota int `json:"quota"`
}

// GetUserQingIdUpdateQuota
// 用户qing id 的可修改次数
func (p *ProfileController) GetUserQingIdUpdateQuota(ctx iris.Context) {
	//qid, _ := ctx.Values().GetInt64("x-qid")
	response := GetUserQingIdUpdateQuotaResponse{
		Quota: 1,
	}
	utils.SuccessWithMsg(ctx, response, "")
	return
}
