package controllers

import (
	"database/sql"
	"fmt"
	"qing-profiles/config"
	"qing-profiles/database"
	"qing-profiles/utils"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

// PasswordInitializeRequest 表示密码初始化请求的结构
type PasswordInitializeRequest struct {
	Password string `json:"pwd" validate:"required"`
}

// PasswordResetRequest 表示密码重置请求的结构
type PasswordResetRequest struct {
	Phone string `json:"phone" validate:"required"`
	Code  string `json:"code" validate:"required"`
	Pwd   string `json:"pwd" validate:"required"`
}

// SmsOtpVerifyRequest 表示SMS验证码验证请求的结构
type SmsOtpVerifyRequest struct {
	Phone string `json:"phone" validate:"required"`
	Code  string `json:"code" validate:"required"`
}

// InitializePassword 处理 POST /api/users/profile/pwd-initialize
// 允许用户在注册后设置初始密码
func (p *ProfileController) InitializePassword(ctx iris.Context) {
	// 从中间件获取用户ID（由CheckAuthorization中间件处理JWT后设置）
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req PasswordInitializeRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("InitializePassword 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	// 修剪字段的空白字符
	req.Password = strings.TrimSpace(req.Password)

	// 验证必填字段
	if req.Password == "" {
		utils.InvalidRequestError(ctx, "密码是必填项")
		return
	}

	logrus.WithFields(logrus.Fields{
		"qid": qid,
		"pwd": req.Password,
	}).Debug("用户进行初始化密码")

	// 获取用户资料
	userinfo, err := findUserByQid(qid)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid": qid,
		}).Error("InitializePassword findUserByQid 获取用户资料失败")

		utils.InternalError(ctx, "")
		return
	}

	if userinfo == nil {
		logrus.WithFields(logrus.Fields{
			"qid": qid,
		}).Error("InitializePassword findUserByQid 用户不存在")

		utils.InvalidRequestError(ctx, "")
		return
	}

	// 验证用户是否已经初始化密码
	credentialInfo, err := getUserPasswordCredential(qid)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid": qid,
		}).Error("InitializePassword getUserPasswordCredential 获取用户密码失败")

		utils.InternalError(ctx, "")
		return
	}

	// 如果用户已经初始化过密码，返回错误
	if credentialInfo != nil {
		logrus.WithFields(logrus.Fields{
			"qid": qid,
		}).Error("用户已有密码，不需要再执行初始化")

		utils.InvalidRequestError(ctx, "")
		return
	}

	// 直接存储密码（密码已经是对称加密后的，不需要再次加密）
	if err = p.storePasswordCredential(qid, userinfo.Phone, req.Password); err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid": qid,
		}).Error("InitializePassword storePasswordCredential 存储密码凭证失败")
		utils.InternalError(ctx, "")
		return
	}

	// 记录成功的密码初始化
	logrus.WithFields(logrus.Fields{
		"qid": qid,
	}).Info("密码初始化成功")

	// 返回成功响应
	utils.SuccessWithMsg(ctx, nil, "")
}

// ResetPassword 处理 POST /api/users/profile/pwd-reset
// 允许用户重置现有密码
func (p *ProfileController) ResetPassword(ctx iris.Context) {
	// Get device ID from context
	deviceID := ctx.Values().GetString("x-device-id")

	var req PasswordResetRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("ResetPassword 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	// 修剪字段的空白字符
	req.Phone = strings.TrimSpace(req.Phone)
	req.Code = strings.TrimSpace(req.Code)
	req.Pwd = strings.TrimSpace(req.Pwd)

	// 验证必填字段
	if req.Phone == "" {
		utils.InvalidRequestError(ctx, "手机号是必填项")
		return
	}

	if req.Code == "" {
		utils.InvalidRequestError(ctx, "验证码是必填项")
		return
	}

	if req.Pwd == "" {
		utils.InvalidRequestError(ctx, "密码是必填项")
		return
	}

	// 验证手机号格式
	if !utils.ValidatePhoneNumber(req.Phone) {
		utils.InvalidRequestError(ctx, "手机号格式无效")
		return
	}

	tokenKey := buildResetPasswordTokenKey(req.Phone, deviceID)
	// 从Redis获取存储的验证码
	storedToken, err := database.GetCache(tokenKey)
	if err != nil || storedToken == "" || req.Code != storedToken {
		// 验证码不存在或已过期
		logrus.WithFields(logrus.Fields{
			"phone":       req.Phone,
			"code":        req.Code,
			"device_id":   deviceID,
			"redis_key":   tokenKey,
			"storedToken": storedToken,
		}).Warn("验证码不存在或已过期")
		utils.InvalidRequestError(ctx, "验证码错误")
		return
	}

	// 获取用户资料
	userinfo, err := findUserByPhone(req.Phone)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"phone": req.Phone,
			"code":  req.Code,
		}).Error("ResetPassword findUserByPhone 失败")
		utils.InternalError(ctx, "")
		return
	}

	// 用户不存在
	if userinfo == nil {
		logrus.WithFields(logrus.Fields{
			"phone": req.Phone,
			"code":  req.Code,
		}).Warn("ResetPassword 用户不存在")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 查找用户是否有密码凭证
	credentialInfo, err := getUserPasswordCredential(userinfo.Qid)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"phone": req.Phone,
			"code":  req.Code,
		}).Error("ResetPassword getUserPasswordCredential 失败")

		utils.InternalError(ctx, "")
		return
	}

	// 用户没有密码，无法重置
	if credentialInfo == nil {
		logrus.WithFields(logrus.Fields{
			"phone": req.Phone,
			"code":  req.Code,
			"qid":   userinfo.Qid,
		}).Info("用户没有密码，但是有账户，此时应该初始化密码")
		// 初始化密码
		err = p.storePasswordCredential(userinfo.Qid, req.Phone, req.Pwd)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":   userinfo.Qid,
				"phone": req.Phone,
				"code":  req.Code,
			}).Error("初始化密码凭证失败")
			utils.InternalError(ctx, "")
			return
		}
		return
	} else {
		// 更新用户的密码凭证
		err = p.updatePasswordCredential(userinfo.Qid, req.Phone, req.Pwd)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":   userinfo.Qid,
				"phone": req.Phone,
				"code":  req.Code,
			}).Error("更新密码凭证失败")
			utils.InternalError(ctx, "")
			return
		}
	}

	// 记录成功的密码重置
	logrus.WithFields(logrus.Fields{
		"qid":   userinfo.Qid,
		"phone": req.Phone,
		"code":  req.Code,
	}).Info("密码重置成功")

	// 返回成功响应
	utils.SuccessWithMsg(ctx, nil, "")
}

// VerifySmsOtp 处理 POST /api/users/profile/pwd-reset/verify-sms-otp
// 验证用户在密码重置前收到的SMS验证码
func (p *ProfileController) VerifySmsOtp(ctx iris.Context) {
	deviceID := ctx.Values().GetString("x-device-id")

	var req SmsOtpVerifyRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("VerifySmsOtp 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	// 修剪字段的空白字符
	req.Phone = strings.TrimSpace(req.Phone)
	req.Code = strings.TrimSpace(req.Code)

	// 验证必填字段
	if req.Phone == "" {
		utils.InvalidRequestError(ctx, "手机号是必填项")
		return
	}

	if req.Code == "" {
		utils.InvalidRequestError(ctx, "验证码是必填项")
		return
	}

	// 验证手机号格式
	if !utils.ValidatePhoneNumber(req.Phone) {
		utils.InvalidRequestError(ctx, "手机号格式无效")
		return
	}

	// 验证验证码格式（6位数字）
	if !p.validateOtpFormat(req.Code) {
		utils.InvalidRequestError(ctx, "验证码格式无效，应为6位数字")
		return
	}

	// 获取设备ID用于构建Redis键
	// 验证SMS验证码
	valid, err := p.verifySmsOtpCode(ScenarioForgetPwd, req.Phone, req.Code, deviceID)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"scenario":  ScenarioForgetPwd,
			"phone":     req.Phone,
			"device_id": deviceID,
			"code":      req.Code,
		}).Error("VerifySmsOtp verifySmsOtpCode 验证SMS验证码时发生错误")
		utils.InvalidRequestError(ctx, "验证码错误")
		return
	}

	// 验证不通过
	if !valid {
		logrus.WithFields(logrus.Fields{
			"scenario":  ScenarioForgetPwd,
			"phone":     req.Phone,
			"device_id": deviceID,
			"code":      req.Code,
		}).Warn("VerifySmsOtp valid==false SMS验证码验证失败")

		utils.InvalidRequestError(ctx, "验证码错误")
		return
	}

	// 验证通过后，保存到redis一个key，用于稍后重置密码时的验证
	tokenKey := buildResetPasswordTokenKey(req.Phone, deviceID)
	// 验证后，需要在10分钟内输入密码
	err = p.saveResetPasswordToken(tokenKey, req.Code)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"phone":     req.Phone,
			"device_id": deviceID,
		}).Error("VerifySmsOtp saveResetPasswordToken 失败")
		utils.InternalError(ctx, "")
		return
	}

	// 记录成功的验证码验证
	logrus.WithFields(logrus.Fields{
		"phone":     req.Phone,
		"device_id": deviceID,
	}).Info("SMS验证码验证成功")

	// 返回成功响应
	utils.SuccessWithMsg(ctx, nil, "")
}

// updatePasswordCredential 更新数据库中的密码凭证
func (p *ProfileController) updatePasswordCredential(qid int64, phone, encryptedPassword string) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_credentials
		SET crdt_value = $1
		WHERE crdt_type = 1 AND qid = $2 AND crdt_key = $3
	`

	_, err := db.Exec(
		query,
		encryptedPassword,
		qid,
		phone,
	)

	if err != nil {
		return err
	}

	return nil
}

// validateOtpFormat 验证验证码格式（6位数字）
func (p *ProfileController) validateOtpFormat(code string) bool {
	// 检查长度
	if len(code) != 6 {
		return false
	}

	// 检查是否全为数字
	for _, char := range code {
		if char < '0' || char > '9' {
			return false
		}
	}

	return true
}

// verifySmsOtpCode 验证SMS验证码
func (p *ProfileController) verifySmsOtpCode(scenario, phone, code, deviceID string) (bool, error) {
	if config.GetConfig().IsProduction() == false {
		if code == "666666" {
			return true, nil
		}
	}

	// 构建Redis键（使用forget_pwd场景）
	otpKey := buildOTPRedisKey(scenario, phone, deviceID)

	// 从Redis获取存储的验证码
	storedCode, err := database.GetCache(otpKey)
	if err != nil || storedCode == "" {
		// 验证码不存在或已过期
		logrus.WithFields(logrus.Fields{
			"scenario":  scenario,
			"phone":     phone,
			"code":      code,
			"device_id": deviceID,
			"redis_key": otpKey,
		}).Debug("验证码不存在或已过期")
		return false, nil
	}

	// 比较验证码
	if storedCode != code {
		logrus.WithFields(logrus.Fields{
			"phone":       phone,
			"device_id":   deviceID,
			"input_code":  code,
			"stored_code": storedCode,
		}).Debug("验证码不匹配")
		return false, nil
	}

	// 验证成功，删除Redis中的验证码防止重复使用
	if err := database.DeleteCache(otpKey); err != nil {
		logrus.WithError(err).Warn("删除已验证的验证码失败")
		// 不返回错误，因为验证已经成功
	}

	logrus.WithFields(logrus.Fields{
		"scenario":  scenario,
		"phone":     phone,
		"code":      code,
		"device_id": deviceID,
		"redis_key": otpKey,
	}).Debug("验证码验证成功并已删除")

	return true, nil
}

// saveResetPasswordToken 保存密码重置令牌
func (p *ProfileController) saveResetPasswordToken(tokenKey, token string) error {
	if err := database.SetCache(tokenKey, token, 10*time.Minute); err != nil {
		return err
	}
	return nil
}

func buildResetPasswordTokenKey(phone, deviceID string) string {
	return fmt.Sprintf("reset:pwd:%v:%v", phone, deviceID)
}

// storePasswordCredential 在数据库中存储密码凭证
func (p *ProfileController) storePasswordCredential(qid int64, phone, encryptedPassword string) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	// 使用手机号作为密码凭证的crdt_key
	credentialKey := phone

	// 将expire_at设置为遥远的未来（从现在起100年）用于非过期密码
	expireAt := 4891334400000 // 2125-01-01 00:00:00（毫秒）

	now := time.Now().UnixMilli()
	query := `
		INSERT INTO user_credentials (qid, crdt_type, crdt_key, crdt_value, created_at, expire_at, status, extra)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`

	_, err := db.Exec(
		query,
		qid,
		CredentialTypePassword,
		credentialKey,
		encryptedPassword,
		now,
		expireAt,
		0,
		nil,
	)

	if err != nil {
		return err
	}

	return nil
}
