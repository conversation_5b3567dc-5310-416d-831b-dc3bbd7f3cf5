package controllers

import (
	"fmt"
	"sort"
)

type MonthInfo struct {
	Month  string `json:"month"`
	MaxDay int    `json:"max_day"`
}

type YearInfo struct {
	Year     string      `json:"year"`
	Months   []MonthInfo `json:"months"`
	Disabled bool        `json:"disabled"`
}

type YearGroup struct {
	Decade string     `json:"decade"`
	Years  []YearInfo `json:"years"`
}

// isLeapYear 判断是否为闰年
func isLeapYear(year int) bool {
	return (year%400 == 0) || (year%4 == 0 && year%100 != 0)
}

// getMonthDays 获取指定年份月份的天数
func getMonthDays(year, month int) int {
	switch month {
	case 1, 3, 5, 7, 8, 10, 12: // 大月
		return 31
	case 4, 6, 9, 11: // 小月
		return 30
	case 2: // 二月
		if isLeapYear(year) {
			return 29 // 闰年
		}
		return 28 // 平年
	default:
		return 0 // 无效月份
	}
}

// generateMonthsForYear 为指定年份生成所有月份信息
func generateMonthsForYear(year int) []MonthInfo {
	var months []MonthInfo

	for month := 1; month <= 12; month++ {
		maxDay := getMonthDays(year, month)
		months = append(months, MonthInfo{
			Month:  fmt.Sprintf("%d", month),
			MaxDay: maxDay,
		})
	}

	return months
}

// generateYearGroups 根据最小和最大年份生成年代分组（包含月份信息）
func generateYearGroups(minYear, maxYear int) []YearGroup {
	if minYear > maxYear {
		return []YearGroup{}
	}

	// 使用 map 来收集每个年代的年份
	decadeMap := make(map[int][]int)
	// 遍历年份范围，按年代分组
	for year := minYear; year <= maxYear; year++ {
		decade := (year / 10) * 10 // 计算年代起始年份
		decadeMap[decade] = append(decadeMap[decade], year)
	}

	// 获取所有年代并排序
	var decades []int
	for decade := range decadeMap {
		decades = append(decades, decade)
	}
	sort.Ints(decades)
	sort.Slice(decades, func(i, j int) bool {
		return decades[i] > decades[j] // 注意这里是 > 号
	})

	// 构建结果
	var result []YearGroup
	for index, decade := range decades {
		years := decadeMap[decade]
		if len(years) <= 0 {
			continue
		}

		sort.Ints(years) // 确保年份有序

		// 为每个年份生成年份信息（包含月份）
		var yearInfos []YearInfo
		for _, year := range years {
			months := generateMonthsForYear(year)
			yearInfos = append(yearInfos, YearInfo{
				Year:   fmt.Sprintf("%d", year),
				Months: months,
			})
		}

		// 不足10年的，补足10年, 只需要补第一个和最后一个数组
		if len(yearInfos) < 10 {
			if index == 0 {
				n := 10 - len(yearInfos)
				last := years[len(years)-1]
				for i := 1; i <= n; i++ {
					year := last + i
					months := generateMonthsForYear(year)
					yearInfos = append(yearInfos, YearInfo{
						Year:     fmt.Sprintf("%d", year),
						Months:   months,
						Disabled: true,
					})
				}
			} else if index == len(decades)-1 {
				n := 10 - len(yearInfos)
				first := years[0]
				for i := 1; i <= n; i++ {
					year := first - i
					months := generateMonthsForYear(year)
					// 追加到数据前面
					yearInfos = append([]YearInfo{{
						Year:   fmt.Sprintf("%d", year),
						Months: months,
						//Disabled: true, // 考虑到岁数大点也无所谓，这里就让可选也行
					}}, yearInfos...)
				}
				yearInfos = append(yearInfos, YearInfo{
					Year:     "none",
					Months:   []MonthInfo{},
					Disabled: false,
				})
			}
		}

		decadeName := fmt.Sprintf("%02d", decade%100)
		result = append(result, YearGroup{
			Decade: decadeName,
			Years:  yearInfos,
		})
	}

	return result
}
