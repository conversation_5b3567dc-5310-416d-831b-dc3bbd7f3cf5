package controllers

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"qing-profiles/database"
	"qing-profiles/utils"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

// ProfileController 处理用户资料相关的端点
type ProfileController struct{}

// NewProfileController 创建新的用户资料控制器
func NewProfileController() *ProfileController {
	return &ProfileController{}
}

// ProfileInitializeRequest 表示用户资料初始化请求的结构
type ProfileInitializeRequest struct {
	MediaId  string `json:"media_id" validate:"required"`
	Username string `json:"username" validate:"required"`
	RoleId   string `json:"role_id" validate:"required"`
}

// InitializeProfile 处理 POST /api/users/profile/initialize
// 初始化用户基本资料信息
func (p *ProfileController) InitializeProfile(ctx iris.Context) {
	// 从中间件获取用户ID
	qid, _ := ctx.Values().GetInt64("x-qid")
	//deviceID := ctx.Values().GetString("x-device-id")

	var req ProfileInitializeRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("InitializeProfile 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	// 修剪字段的空白字符
	req.MediaId = strings.TrimSpace(req.MediaId)
	req.Username = strings.TrimSpace(req.Username)
	req.RoleId = strings.TrimSpace(req.RoleId)

	// 验证必填字段
	if req.MediaId == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	if req.Username == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 验证role参数范围
	intRole, err := p.toIntRole(req.RoleId)
	if err != nil {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 检查AvatarId
	mediaId, err := utils.DecodeMediaID(req.MediaId)
	if err != nil || mediaId <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"media_id": req.MediaId,
			"username": req.Username,
			"role_id":  req.RoleId,
			"mediaId":  mediaId,
		}).Error("InitializeProfile DecodeMediaID 失败")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 获取用户资料
	userinfo, err := findUserByQid(qid)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid": qid,
		}).Error("InitializePassword findUserByQid 获取用户资料失败")

		utils.InternalError(ctx, "")
		return
	}

	if userinfo == nil {
		logrus.WithFields(logrus.Fields{
			"qid": qid,
		}).Error("InitializePassword findUserByQid 用户不存在")

		utils.InvalidRequestError(ctx, "")
		return
	}

	// TODO 调试期间先移除这个逻辑
	// 检查用户是否已经初始化过资料
	//requiresProfileCompletion := userinfo.HasInitialized == false
	//if requiresProfileCompletion == false {
	//	logrus.WithFields(logrus.Fields{
	//		"qid":      qid,
	//		"media_id": req.MediaId,
	//		"username": req.Username,
	//		"role_id":  req.RoleId,
	//		"mediaId":  mediaId,
	//	}).Error("用户资料已经初始化过")
	//	utils.InvalidRequestError(ctx, "")
	//	return
	//}

	// TODO 需要对 req.Username 进行机器审核

	userGenMedia, err := getUploadMediaInfo(mediaId, qid)
	if err != nil || userGenMedia == nil || userGenMedia.MediaObjectKey == "" {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"media_id": req.MediaId,
			"username": req.Username,
			"role_id":  req.RoleId,
		}).Error("InitializeProfile getUploadMediaInfo 失败")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 场景 1:设置头像 2:发布动态
	// 状态 -2:已删除 -1:未通过审核 0:默认 1:通过机审 2:已使用 3:已审核
	if userGenMedia.Qid != qid || userGenMedia.Scenario != 1 || userGenMedia.Status != 1 {
		logrus.WithFields(logrus.Fields{
			"qid":      qid,
			"media_id": req.MediaId,
			"username": req.Username,
			"role_id":  req.RoleId,
		}).Error("InitializeProfile 媒体资源不符合预期")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 初始化用户的资料，更新角色，用户的头像、昵称添加到待审核列表
	if err = p.saveUserInitialProfile(qid, userGenMedia.MediaId, userGenMedia.MediaObjectKey, req.Username, intRole); err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"media_id": req.MediaId,
			"username": req.Username,
			"role_id":  req.RoleId,
			"avatar":   userGenMedia.MediaObjectKey,
		}).Error("InitializeProfile 插入审核表失败")
		utils.InternalError(ctx, "")
		return
	}

	// 保存到缓存
	err = p.saveUserProfileToCache(qid, map[string]string{
		"pending:avatar_object_key": userGenMedia.MediaObjectKey,
		"pending:avatar_media_id":   fmt.Sprint(mediaId),
		"pending:username":          req.Username,
		"role":                      fmt.Sprint(intRole),
	})
	if err != nil {
		// TODO 这是一个重要的错误，需要及时发出通知
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"avatar":   userGenMedia.MediaObjectKey,
			"username": req.Username,
			"role":     req.RoleId,
		}).Error("saveUserProfileToCache 保存到缓存失败")
	}

	// 记录成功的资料初始化
	logrus.WithFields(logrus.Fields{
		"qid":      qid,
		"media_id": req.MediaId,
		"username": req.Username,
		"role_id":  req.RoleId,
		"avatar":   userGenMedia.MediaObjectKey,
	}).Info("InitializeProfile 用户资料初始化成功")

	// 返回成功响应
	utils.SuccessWithMsg(ctx, nil, "")
}

// saveUserInitialProfile 保存用户初始化资料
func (p *ProfileController) saveUserInitialProfile(qid int64, mediaId int64, objectKey, username string, role int) error {
	now := time.Now().UnixMilli()
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	taskId1 := utils.GeneratePendingReviewTaskID()
	taskId2 := utils.GeneratePendingReviewTaskID()

	// 开启事务
	tx, err := db.Begin()
	if err != nil {
		return err
	}

	// 更新用户角色
	{
		query := `
			UPDATE user_basic_profiles
			SET role = $1, has_initialized = $2
			WHERE qid = $3
		`

		_, err = tx.Exec(query, role, true, qid)
		if err != nil {
			_ = tx.Rollback()
			return err
		}
	}
	// 插入头像审核记录
	{
		scenario := 2 // 场景 1:修改qingID 2:修改头像 3:修改昵称 4:修改简介 5:修改个人主页封面 6:修改好友圈封面 等等
		status := 0   // 状态 0:待审核 1:审核通过 -1:审核不通过 -2:用户已取消(比如新待审头像覆盖了旧待审头像)

		var extra any
		if extraBytes, mErr := json.Marshal(&PendingReviewAvatarExtra{
			MediaId: mediaId,
		}); mErr == nil {
			extra = string(extraBytes)
		}

		insertAvatarQuery := `
			INSERT INTO pending_review_profiles (task_id, qid, scenario, content, created_at, status, extra)
			VALUES ($1, $2, $3, $4, $5, $6, $7)
		`
		_, err = tx.Exec(insertAvatarQuery, taskId1, qid, scenario, objectKey, now, status, extra)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":       qid,
				"mediaId":   mediaId,
				"objectKey": objectKey,
			}).Error("savePendingReviewAvatar 插入审核表失败")
			_ = tx.Rollback()
			return err
		}
	}

	// 插入昵称审核记录
	{
		scenario := 3 // 3:修改昵称
		status := 0   // 状态 0:待审核 1:审核通过 -1:审核不通过 -2:用户已取消(比如新待审头像覆盖了旧待审头像)
		insertUsernameQuery := `
			INSERT INTO pending_review_profiles (task_id, qid, scenario, content, created_at, status, extra)
			VALUES ($1, $2, $3, $4, $5, $6, $7)
		`
		_, err = tx.Exec(insertUsernameQuery, taskId2, qid, scenario, username, now, status, nil)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":       qid,
				"mediaId":   mediaId,
				"objectKey": objectKey,
				"taskId":    taskId2,
			}).Error("savePendingReviewAvatar 插入审核表失败")
			_ = tx.Rollback()
			return err
		}
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":       qid,
			"mediaId":   mediaId,
			"objectKey": objectKey,
		}).Error("savePendingReviewAvatarAndUsername 提交事务失败")
		_ = tx.Rollback()
		return err
	}

	return nil
}

type GetUserProfileDetailsResponse struct {
	Avatar                 string       `json:"avatar"`
	PendingAvatar          string       `json:"pending_avatar"`
	Username               string       `json:"username"`
	PendingUsername        string       `json:"pending_username"`
	Qid                    string       `json:"qid"`
	CustomQid              string       `json:"custom_qid"`
	PendingCustomQid       string       `json:"pending_custom_qid"`
	Bio                    string       `json:"bio"`
	PendingBio             string       `json:"pending_bio"`
	BirthdayId             string       `json:"birthday_id"`
	BirthdayTags           []ProfileTag `json:"birthday_tags"`
	BirthdayBlur           bool         `json:"birthday_blur"`
	ZodiacSignId           string       `json:"zodiac_sign_id"`
	ZodiacSignTags         []ProfileTag `json:"zodiac_sign_tags"`
	HeightId               string       `json:"height_id"`
	HeightTags             []ProfileTag `json:"height_tags"`
	HeightBlur             bool         `json:"height_blur"`
	WeightId               string       `json:"weight_id"`
	WeightTags             []ProfileTag `json:"weight_tags"`
	WeightBlur             bool         `json:"weight_blur"`
	RoleId                 string       `json:"role_id"`
	RoleTags               []ProfileTag `json:"role_tags"`
	RegularCityId          string       `json:"regular_city_id"`
	RegularCityTags        []ProfileTag `json:"regular_city_tags"`
	HometownId             string       `json:"hometown_id"`
	HometownTags           []ProfileTag `json:"hometown_tags"`
	MBTIId                 string       `json:"mbti_id"`
	MBTITags               []ProfileTag `json:"mbti_tags"`
	PersonalityIds         []string     `json:"personality_ids"`
	PersonalityTags        []ProfileTag `json:"personality_tags"`
	InterestIds            []string     `json:"interest_ids"`
	InterestTags           []ProfileTag `json:"interest_tags"`
	RelationshipStatusIds  []string     `json:"relationship_status_ids"`
	RelationshipStatusTags []ProfileTag `json:"relationship_status_tags"`
	FutureGoalIds          []string     `json:"future_goal_ids"`
	FutureGoalTags         []ProfileTag `json:"future_goal_tags"`
}

type ProfileTag struct {
	Name  string `json:"name"`
	Color string `json:"color"`
}

// GetUserProfileDetails
// 获取用户资料详情
func (p *ProfileController) GetUserProfileDetails(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	// 直接从redis获取缓存
	userinfo, err := p.getUserProfileCache(qid)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid": qid,
		}).Error("getUserProfileCache 获取用户资料缓存失败")
		utils.InternalError(ctx, "")
		return
	}

	// 加密qid
	encryptedQid, _ := utils.EncodeQID(qid)

	// 把生日专为整型，然后再专为yyyy-m-d
	birthdayId := parseBirthdayToBirthdayId(userinfo["birthday"])
	birthdayBlur, _ := strconv.ParseBool(userinfo["birthday_blur"])
	birthdayTags := parseBirthdayTags(userinfo["birthday"], userinfo["birthday_blur"])

	// 星座
	zodiacSignId := ""
	zodiacSignTags := make([]ProfileTag, 0)
	zodiacSignInt, _ := strconv.Atoi(userinfo["zodiac_sign"])
	if zodiacSignInt > 0 {
		zodiacSignId, _ = toStringZodiacSign(zodiacSignInt)
		zodiacSignItem := getZodiacSignById(zodiacSignId)
		zodiacSignTags = []ProfileTag{
			{
				Name:  zodiacSignItem.Name,
				Color: zodiacSignItem.TagColor,
			},
		}
	}

	response := GetUserProfileDetailsResponse{
		Avatar:           utils.GetOriginAvatarUrl(userinfo["avatar_object_key"]),
		PendingAvatar:    utils.GetOriginAvatarUrl(userinfo["pending:avatar_object_key"]),
		Username:         userinfo["username"],
		PendingUsername:  userinfo["pending:username"],
		Qid:              encryptedQid,
		CustomQid:        userinfo["custom_qid"],
		PendingCustomQid: userinfo["pending:custom_qid"],
		Bio:              userinfo["bio"],
		PendingBio:       userinfo["pending:bio"],
		BirthdayId:       birthdayId,
		BirthdayTags:     birthdayTags,
		BirthdayBlur:     birthdayBlur,
		ZodiacSignId:     zodiacSignId,
		ZodiacSignTags:   zodiacSignTags,
		HeightId:         userinfo["height"],
		HeightTags: []ProfileTag{
			{
				Name:  "身高180cm",
				Color: "#E3FAFF",
			},
		},
		HeightBlur: false,
		WeightId:   userinfo["weight"],
		WeightTags: []ProfileTag{
			{
				Name:  "体重70kg",
				Color: "#E3FAFF",
			},
		},
		WeightBlur: false,
		RoleId:     "1",
		RoleTags: []ProfileTag{
			{
				Name:  "1",
				Color: "#E3FAFF",
			},
		},
		RegularCityId: "13-1305-130525",
		RegularCityTags: []ProfileTag{
			{
				Name:  "河北·邢台",
				Color: "#E3FAFF",
			},
		},
		HometownId: "13-1305-130525",
		HometownTags: []ProfileTag{
			{
				Name:  "河北·邢台",
				Color: "#E3FAFF",
			},
		},
		MBTIId: "intj",
		MBTITags: []ProfileTag{
			{
				Name:  "INTJ",
				Color: "#E3FAFF",
			},
		},
		PersonalityIds: []string{"1"},
		PersonalityTags: []ProfileTag{
			{
				Name:  "👨人夫脸",
				Color: "#E3FAFF",
			},
			{
				Name:  "🍳会做饭",
				Color: "#E3FAFF",
			},
			{
				Name:  "💪脂包鸡",
				Color: "#E3FAFF",
			},
			{
				Name:  "👦男大",
				Color: "#FFE6E6",
			},
			{
				Name:  "声音好听",
				Color: "#EDFFEF",
			},
			{
				Name:  "喜欢过直男",
				Color: "#E3FAFF",
			},
		},
		InterestIds: []string{"1"},
		InterestTags: []ProfileTag{
			{
				Name:  "🥏飞盘",
				Color: "#E3FAFF",
			},
			{
				Name:  "🐶养狗",
				Color: "#DCFFD8",
			},
			{
				Name:  "🐱养猫",
				Color: "#EDE0D4",
			},
		},
		RelationshipStatusIds: []string{"1"},
		RelationshipStatusTags: []ProfileTag{
			{
				Name:  "单身",
				Color: "#E3FAFF",
			},
		},

		FutureGoalIds: []string{"1"},
		FutureGoalTags: []ProfileTag{
			{
				Name:  "对所有人出柜",
				Color: "#E3FAFF",
			},
		},
	}
	utils.SuccessWithMsg(ctx, response, "")
	return
}

func parseBirthdayToBirthdayId(birthday string) string {
	if birthday == "" {
		return ""
	}
	birthdayInt, err := strconv.ParseInt(birthday, 10, 64)
	if err != nil {
		return ""
	}

	birthdayTime := time.Unix(birthdayInt, 0)
	birthdayStr := birthdayTime.Format("2006-01-02")
	birthdayParts := strings.Split(birthdayStr, "-")

	if len(birthdayParts) != 3 {
		return ""
	}

	birthdaySlice := make([]string, 0)
	for _, part := range birthdayParts {
		partInt, err := strconv.ParseInt(part, 10, 64)
		if err != nil {
			return ""
		}
		birthdaySlice = append(birthdaySlice, fmt.Sprint(partInt))
	}
	return strings.Join(birthdaySlice, "-")
}

func parseBirthdayTags(birthday string, blur string) []ProfileTag {
	if birthday == "" {
		return []ProfileTag{}
	}
	birthdayInt, err := strconv.ParseInt(birthday, 10, 64)
	if err != nil {
		return []ProfileTag{}
	}

	birthdayTime := time.Unix(birthdayInt, 0)
	birthdayStr := birthdayTime.Format("2006-01-02")
	birthdayParts := strings.Split(birthdayStr, "-")

	if len(birthdayParts) != 3 {
		return []ProfileTag{}
	}

	birthdayIntSlice := make([]int, 0)
	for _, part := range birthdayParts {
		partInt, err := strconv.ParseInt(part, 10, 64)
		if err != nil {
			return []ProfileTag{}
		}
		birthdayIntSlice = append(birthdayIntSlice, int(partInt))
	}

	year := birthdayIntSlice[0]
	month := birthdayIntSlice[1]
	day := birthdayIntSlice[2]
	age := time.Now().Year() - year

	if blur == "true" {
		return []ProfileTag{
			{
				Name:  "00后",
				Color: "#E3FAFF",
			},
			{
				Name:  fmt.Sprintf("%d月%d日", month, day),
				Color: "#E3FAFF",
			},
		}
	}

	return []ProfileTag{
		{
			Name:  "年龄" + fmt.Sprintf("%d", age),
			Color: "#E3FAFF",
		},
		{
			Name:  fmt.Sprintf("%d月%d日", month, day),
			Color: "#E3FAFF",
		},
	}
}

// GetUserProfileBasicInfo
// 获取用户基本资料
func (p *ProfileController) GetUserProfileBasicInfo(ctx iris.Context) {

}
