package controllers

import (
	"database/sql"
	"encoding/json"
	"qing-profiles/database"
	"qing-profiles/models"
	"qing-profiles/s4"
	"qing-profiles/utils"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

const MaxImageDataSize = 10 * 1024 * 1024  // 10M
const MaxVideoDataSize = 100 * 1024 * 1024 // 10M
const MinImageWidth = 1000
const MinImageHeight = 1000

type GetMediaUploadURLRequest struct {
	MediaType string `json:"media_type"` // image video
	FileType  string `json:"file_type"`  // jpg png
	FileSize  int64  `json:"file_size"`
	Width     int64  `json:"width"`
	Height    int64  `json:"height"`
	Duration  int64  `json:"duration"`
}
type GetMediaUploadURLResponse struct {
	UploadURL string `json:"upload_url"`
	MediaId   string `json:"media_id"`
}

type UserPendingMedia struct {
	MediaId    int64  `json:"media_id"`
	Scenario   string `json:"scenario"`
	MediaType  string `json:"media_type"`
	ObjectKey  string `json:"object_key"`
	FileSize   int64  `json:"file_size"`
	Width      int64  `json:"width"`
	Height     int64  `json:"height"`
	Duration   int64  `json:"duration"`
	VideoCover string `json:"video_cover"`
	Thumbhash  string `json:"thumbhash"`
	Exif       any    `json:"exif"`
	AiDesc     any    `json:"ai_desc"`
	OCR        any    `json:"ocr"`
	Extra      any    `json:"extra"`
}

// MediaController 处理用户媒体相关的端点
type MediaController struct{}

// NewMediaController 创建新的媒体控制器
func NewMediaController() *MediaController {
	return &MediaController{}
}

// GetUploadMediaURL
// 获取上传媒体的URL
func (m *MediaController) GetUploadMediaURL(ctx iris.Context) {
	// 从中间件获取用户ID（由CheckAuthorization中间件处理JWT后设置）
	qid, _ := ctx.Values().GetInt64("x-qid")
	deviceID := ctx.Values().GetString("x-device-id")
	scenario := strings.TrimSpace(ctx.Params().Get("scenario"))

	var req GetMediaUploadURLRequest
	req.MediaType = strings.TrimSpace(ctx.URLParamDefault("media_type", "image"))
	req.FileType = strings.TrimSpace(ctx.URLParam("file_type"))
	req.FileSize, _ = ctx.URLParamInt64("file_size")
	req.Width, _ = ctx.URLParamInt64("width")
	req.Height, _ = ctx.URLParamInt64("height")
	req.Duration, _ = ctx.URLParamInt64("duration")

	if req.Duration < 0 || req.MediaType != "image" {
		req.Duration = 0
	}

	if req.MediaType != "image" && req.MediaType != "video" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	if req.FileSize <= 0 {
		logrus.WithFields(logrus.Fields{
			"qid":       qid,
			"file_type": req.FileType,
			"file_size": req.FileSize,
			"width":     req.Width,
			"height":    req.Height,
		}).Error("GetMediaUploadURL FileSize 无效")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 图片最大不能超多10M 视频最大不能超多100M
	if req.MediaType == "image" && req.FileSize > MaxImageDataSize || req.MediaType == "video" && req.FileSize > MaxVideoDataSize {
		logrus.WithFields(logrus.Fields{
			"qid":       qid,
			"file_type": req.FileType,
			"file_size": req.FileSize,
			"width":     req.Width,
			"height":    req.Height,
		}).Error("GetMediaUploadURL FileSize 图片大小超过限制")
		utils.InvalidRequestError(ctx, "文件大小超过限制")
		return
	}

	// 最小像素
	if req.MediaType == "image" && (req.Width < MinImageWidth || req.Height < MinImageHeight) {
		logrus.WithFields(logrus.Fields{
			"qid":       qid,
			"file_type": req.FileType,
			"file_size": req.FileSize,
			"width":     req.Width,
			"height":    req.Height,
		}).Error("GetMediaUploadURL Width Height 图片尺寸过小")
		utils.InvalidRequestError(ctx, "图片尺寸过小")
		return
	}

	// 最小时长
	if req.MediaType == "video" && req.Duration <= 0 {
		logrus.WithFields(logrus.Fields{
			"qid":       qid,
			"file_type": req.FileType,
			"file_size": req.FileSize,
			"width":     req.Width,
			"height":    req.Height,
		}).Error("GetMediaUploadURL video 的 duration 不能小于等于0")
		utils.InvalidRequestError(ctx, "视频时长太小")
		return
	}

	// 获取用户资料
	userinfo, err := findUserByQid(qid)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid": qid,
		}).Error("GetMediaUploadURL findUserByQid 获取用户资料失败")

		utils.InternalError(ctx, "")
		return
	}

	if userinfo == nil {
		logrus.WithFields(logrus.Fields{
			"qid": qid,
		}).Error("GetMediaUploadURL findUserByQid 用户不存在")

		utils.InvalidRequestError(ctx, "")
		return
	}

	// 生成一个随机的avatar_id
	prefix, suffix := m.getFileTypeInfo(req.FileType)
	objectKey := generateFeedMediaObjectKey(prefix, suffix)

	// 获取上传地址
	uploadUrl := s4.GetUploadUrl(objectKey, req.FileSize)

	mediaId := utils.GenerateMediaID()
	mediaIdStr, err := utils.EncodeMediaID(mediaId)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":     qid,
			"mediaId": mediaId,
		}).Error("GetMediaUploadURL EncodeMediaID 失败")
		utils.InternalError(ctx, "")
		return
	}
	var extra any
	if extraBytes, mErr := json.Marshal(&UserGenMediaExtra{
		DeviceId: deviceID,
	}); mErr == nil {
		extra = string(extraBytes)
	}

	pendingMedia := &UserPendingMedia{
		MediaId:    mediaId,
		Scenario:   scenario,
		MediaType:  req.MediaType,
		ObjectKey:  objectKey,
		FileSize:   req.FileSize,
		Width:      req.Width,
		Height:     req.Height,
		Duration:   req.Duration,
		VideoCover: "",
		Thumbhash:  "",
		Exif:       nil,
		OCR:        nil,
		AiDesc:     nil,
		Extra:      extra,
	}

	// 保存
	err = m.saveUploadMediaInfo(qid, pendingMedia)

	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":     qid,
			"mediaId": mediaId,
		}).Error("GetMediaUploadURL saveMediaUploadInfo 失败")
		utils.InternalError(ctx, "")
		return
	}

	logrus.WithFields(logrus.Fields{
		"qid":        qid,
		"mediaId":    mediaId,
		"mediaIdStr": mediaIdStr,
		"objectKey":  objectKey,
	}).Debug("获取动态媒体文件的上传URL成功")

	response := &GetMediaUploadURLResponse{
		UploadURL: uploadUrl,
		MediaId:   mediaIdStr,
	}

	// 返回成功响应
	utils.SuccessWithMsg(ctx, response, "")
	return
}

type MediaUploadedRequest struct {
	MediaId string `json:"media_id"`
}

type MediaUploadedResponse struct {
	MediaType     string `json:"media_type"`                // image or video
	Valid         bool   `json:"valid"`                     // 是否通过审核
	ImageUrl      string `json:"image_url,omitempty"`       // 图片地址
	ImageThumbUrl string `json:"image_thumb_url,omitempty"` // 缩略图地址
	VideoUrl      string `json:"video_url,omitempty"`       // 图片地址
	VideoCoverUrl string `json:"video_cover_url,omitempty"` // 缩略图地址
}

// MediaUploaded
// 处理媒体上传完成
func (m *MediaController) MediaUploaded(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")
	//deviceID := ctx.Values().GetString("x-device-id")

	var req MediaUploadedRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("MediaUploaded 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	mediaIdStr := strings.TrimSpace(req.MediaId)
	if mediaIdStr == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 解码 mediaId
	mediaId, err := utils.DecodeMediaID(mediaIdStr)
	if err != nil || mediaId <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":     qid,
			"mediaId": mediaIdStr,
		}).Error("MediaUploaded DecodeMediaID 失败")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 获取媒体信息
	userGenMedia, err := getUploadMediaInfo(mediaId, qid)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":          qid,
			"media_id":     req.MediaId,
			"userGenMedia": userGenMedia,
		}).Error("MediaUploaded getUploadMediaInfo 失败")
		utils.InternalError(ctx, "")
		return
	}
	if userGenMedia == nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"media_id": req.MediaId,
		}).Error("MediaUploaded getUploadMediaInfo 未找到媒体资源")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 场景 1:设置头像 2:发布动态
	// 状态 -2:已删除 -1:未通过审核 0:默认 1:通过机审 2:已使用 3:已审核
	if userGenMedia.Qid != qid || userGenMedia.Status != 0 || userGenMedia.MediaObjectKey == "" {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":             qid,
			"media_id":        req.MediaId,
			"media_qid":       userGenMedia.Qid,
			"media_status":    userGenMedia.Status,
			"media_objectKey": userGenMedia.MediaObjectKey,
		}).Error("MediaUploaded 媒体资源不符合预期")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// TODO 对文件做机器审核，再修改状态
	valid := m.auditMedia()

	// 修改媒体状态为可用（已审核）
	// TODO 如果审核不通过，需求修改为未审核通过状态
	err = m.updateMediaStatusToAvailable(mediaId)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"media_id": req.MediaId,
		}).Error("MediaUploaded updateMediaStatusToAvailable 失败")
		utils.InternalError(ctx, "")
		return
	}

	// TODO 从s4获取额外信息，视频封面，thumbhash，exif, OCR, AI描述 等信息，获取后更新到数据库，这里可以做成异步的因为更新这些信息是尽力而为的，没这些信息也没事

	// 构造返回数据
	response := MediaUploadedResponse{
		Valid: valid,
	}

	if userGenMedia.MediaType == 2 {
		response.MediaType = "video"
		response.VideoUrl = utils.GetOriginVideoUrl(userGenMedia.MediaObjectKey)
		// TODO 实时获取视频封面
		response.VideoCoverUrl = utils.GetMiddleFeedImageUrl("fake/video-cover.JPG")
	} else {
		response.MediaType = "image"
		response.ImageUrl = utils.GetOriginFeedImageUrl(userGenMedia.MediaObjectKey)
		response.ImageThumbUrl = utils.GetMiddleFeedImageUrl(userGenMedia.MediaObjectKey)
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

// TODO 机器审核媒体资源
func (m *MediaController) auditMedia() bool {
	return true
}

// getFileTypeInfo 获取文件的格式前后缀 img,jpg
func (m *MediaController) getFileTypeInfo(fileType string) (string, string) {
	if fileType == "" {
		return "file", ""
	}

	// 比如 image/png 返回png, 如果是png直接返回png
	if strings.Contains(fileType, "/") {
		value := fileType[strings.Index(fileType, "/")+1:]
		fileType = strings.TrimSpace(strings.Trim(value, "."))
	}

	if utils.IsImageType(fileType) {
		return "img", fileType
	}

	if utils.IsVideoType(fileType) {
		return "vid", fileType
	}

	return "file", ""
}

// saveUploadMediaInfo 保存媒体上传信息
func (m *MediaController) saveUploadMediaInfo(qid int64, pendingMedia *UserPendingMedia) error {
	now := time.Now().UnixMilli()
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	status := models.UserGenMediaStatusPending // 状态 -2:已删除 -1:未通过审核 0:默认 1:通过机审 2:已使用 3:已审核（如果用户上传了3个图，实际发布动态时只用了两个，那么另外一个图需要设置成-2）
	mediaType := models.FeedMediaTypeImage     // 1:图片 2:视频
	if pendingMedia.MediaType == "video" {
		mediaType = models.FeedMediaTypeVideo
	}

	scenario := models.UserGenMediaScenarioNone // 场景 1:设置头像 2:发布动态 3:个人主页封面 4:好友圈封面
	if pendingMedia.Scenario == "avatar" {
		scenario = models.UserGenMediaScenarioAvatar
	} else if pendingMedia.Scenario == "feed" {
		scenario = models.UserGenMediaScenarioFeed
	} else if pendingMedia.Scenario == "profile-cover" {
		scenario = models.UserGenMediaScenarioProfileCover
	} else if pendingMedia.Scenario == "moments-cover" {
		scenario = models.UserGenMediaScenarioMomentsCover
	}

	insertFeedQuery := `
		INSERT INTO user_gen_medias (media_id, qid, scenario, media_type, media_object_key, file_size, status, created_at, thumbhash, width, height, duration, video_cover, exif, ocr, ai_desc, extra)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
	`
	_, err := db.Exec(insertFeedQuery,
		pendingMedia.MediaId,
		qid,
		scenario,
		mediaType,
		pendingMedia.ObjectKey,
		pendingMedia.FileSize,
		status,
		now,
		pendingMedia.Thumbhash,  // thumbhash
		pendingMedia.Width,      // width
		pendingMedia.Height,     // height
		pendingMedia.Duration,   // duration
		pendingMedia.VideoCover, // video_cover
		pendingMedia.Exif,       // exif
		pendingMedia.OCR,        // ocr
		pendingMedia.AiDesc,     // ai_desc
		pendingMedia.Extra,      // extra
	)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":       qid,
			"mediaId":   pendingMedia.MediaId,
			"objectKey": pendingMedia.ObjectKey,
		}).Error("saveUploadMediaInfo 插入用户生成的媒体失败")
		return err
	}
	return nil
}

// updateMediaStatusToAvailable 更新媒体状态为可用状态(通过机审)
func (m *MediaController) updateMediaStatusToAvailable(mediaId int64) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	status := 1 // 状态 -2:已删除 -1:未通过审核 0:默认 1:通过机审 2:已使用 3:已审核
	query := `UPDATE user_gen_medias SET status = $1 WHERE media_id = $2`
	_, err := db.Exec(query, status, mediaId)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"mediaId": mediaId,
		}).Error("updateMediaStatusToAvailable 更新媒体状态失败")
		return err
	}
	return nil
}
