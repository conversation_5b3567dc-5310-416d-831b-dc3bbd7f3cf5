package controllers

import (
	"qing-profiles/utils"

	"github.com/kataras/iris/v12"
)

// AppConfigController handles app configuration-related endpoints
type AppConfigController struct{}

// NewAppConfigController creates a new app config controller
func NewAppConfigController() *AppConfigController {
	return &AppConfigController{}
}

// BackgroundConfig represents background configuration
type BackgroundConfig struct {
	Type string `json:"type"` // "image" or "video" (future support)
	URL  string `json:"url"`
}

// GetAppConfigResponse represents login page configuration
type GetAppConfigResponse struct {
	Background BackgroundConfig `json:"background"`
}

// GetAppConfig handles GET /api/app/config
// Returns configuration data for the login page including background settings
func (c *AppConfigController) GetAppConfig(ctx iris.Context) {
	// Create the configuration response
	config := GetAppConfigResponse{
		Background: BackgroundConfig{
			Type: "image",
			URL:  "https://qing-test-files.bilifans.com/app/bg.png",
		},
	}

	// Return success response with configuration data
	utils.SuccessWithMsg(ctx, config, "")
}
