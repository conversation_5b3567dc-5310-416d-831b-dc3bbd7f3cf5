package controllers

import (
	"fmt"
	"qing-profiles/aliyun"
	"qing-profiles/database"
	"qing-profiles/utils"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

// SmsController handles SMS-related endpoints
type SmsController struct{}

// NewSmsController creates a new SMS controller
func NewSmsController() *SmsController {
	return &SmsController{}
}

// SmsOtpRequest represents the request structure for SMS OTP
type SmsOtpRequest struct {
	Phone    string `json:"phone" validate:"required"`
	Scenario string `json:"scenario" validate:"required"`
}

// Valid scenarios for SMS OTP
const (
	ScenarioLogin     = "login"
	ScenarioForgetPwd = "forget_pwd"
)

const (
	OtpExpiration = 10 * time.Minute // OTP expiration time
)

// SendOtp handles POST /api/users/login/sms-otp
// Sends SMS OTP verification code to the specified phone number
func (s *SmsController) SendOtp(ctx iris.Context) {
	// Get device ID from context
	deviceID := ctx.Values().GetString("x-device-id")

	var req SmsOtpRequest

	// Parse JSON request body
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("SendOtp 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	// Trim whitespace from string fields to improve input compatibility
	req.Phone = strings.TrimSpace(req.Phone)
	req.Scenario = strings.TrimSpace(req.Scenario)

	// 验证必填字段
	if req.Phone == "" {
		utils.InvalidRequestError(ctx, "手机号是必填项")
		return
	}

	if req.Scenario == "" {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// Validate scenario
	if req.Scenario != ScenarioLogin && req.Scenario != ScenarioForgetPwd {
		utils.InvalidRequestError(ctx, "")
		return
	}

	// Validate a phone number format
	if !utils.ValidatePhoneNumber(req.Phone) {
		utils.InvalidRequestError(ctx, "手机号格式无效")
		return
	}

	// 如果是忘记密码的场景，需要根据用户是否注册来判断是否发送验证码
	if req.Scenario == ScenarioForgetPwd {
		// 检查用户是否注册
		userinfo, err := findUserByPhone(req.Phone)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"phone": req.Phone,
			}).Error("SendOtp findUserByPhone 失败")
			utils.InternalError(ctx, "")
			return
		}

		// 用户不存在就不发验证码，存在才发
		if userinfo == nil {
			// Log successful OTP generation
			logrus.WithFields(logrus.Fields{
				"phone":     req.Phone,
				"device_id": deviceID,
				"scenario":  req.Scenario,
			}).Info("SendOtp 用户不存在，不需要发送验证码")

			utils.SuccessWithMsg(ctx, nil, "")
			return
		}
	}

	// Generate OTP
	otp := utils.GenerateOTP()

	// Store OTP in Redis
	otpKey := buildOTPRedisKey(req.Scenario, req.Phone, deviceID)

	// 在忘记密码的场景下，需要检查用户是否注册，如果没有注册，则告诉用户发送成功，但实际上不会发送验证码，需求：防止用忘记密码来检测是否注册

	// TODO 如果存在，那么就返回之前的验证码
	if err := database.SetCache(otpKey, otp, OtpExpiration); err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"phone":     req.Phone,
			"device_id": deviceID,
			"scenario":  req.Scenario,
			"redis_key": otpKey,
		}).Error("SendOtp SetCache 失败")
		utils.InternalError(ctx, "")
		return
	}

	// Send SMS (don't expose SMS sending errors to a client for security)
	if err := aliyun.SendSmsOtp(req.Phone, otp); err != nil {
		// invalid the otp in redis
		if delErr := database.DeleteCache(otpKey); delErr != nil {
			logrus.WithFields(logrus.Fields{
				"phone":     req.Phone,
				"device_id": deviceID,
				"scenario":  req.Scenario,
				"error":     delErr.Error(),
			}).Error("Failed to delete OTP from Redis")
		}
		logrus.WithFields(logrus.Fields{
			"phone":     req.Phone,
			"device_id": deviceID,
			"scenario":  req.Scenario,
			"error":     err.Error(),
		}).Error("Failed to send SMS OTP")

		utils.InternalError(ctx, "")
		return
	}

	// Log successful OTP generation
	logrus.WithFields(logrus.Fields{
		"phone":     req.Phone,
		"device_id": deviceID,
		"scenario":  req.Scenario,
		"otp":       otp,
	}).Info("发送验证码成功")

	// Return success response (always success for security)
	utils.SuccessWithMsg(ctx, nil, "")
}

// buildOTPRedisKey builds the Redis key for storing OTP
func buildOTPRedisKey(scenario, phone, deviceID string) string {
	return fmt.Sprintf("otp:%s:%s:%s", scenario, phone, deviceID)
}
