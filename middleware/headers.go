package middleware

import (
	"database/sql"
	"errors"
	"qing-profiles/config"
	"qing-profiles/database"
	"qing-profiles/models"
	"qing-profiles/utils"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

// CheckAuthorization 处理鉴权头
func CheckAuthorization() iris.Handler {
	return func(ctx iris.Context) {
		requestPath := ctx.Path()
		// 公开接口不需要鉴权
		if ispPublicAPI(requestPath) {
			ctx.Next()
			return
		}

		authHeader := strings.TrimSpace(ctx.GetHeader("Authorization"))
		logrus.WithFields(logrus.Fields{
			"authHeader": authHeader,
			"path":       ctx.Path(),
		}).Debug("CheckAuthorization Middleware")

		accessToken := strings.TrimPrefix(authHeader, "Bearer ")
		if authHeader == "" {
			logrus.WithFields(logrus.Fields{
				"missing_header": "Authorization",
				"path":           ctx.Path(),
				"method":         ctx.Method(),
				"remote_addr":    ctx.RemoteAddr(),
			}).Warn("缺少必需的Authorization头部")

			utils.UnauthorizedError(ctx, "")
			return
		}

		if config.GetConfig().IsProduction() == false && accessToken == "supertoken" {
			// 测试环境有一个特殊的token,为了调试方便
			// 将用户信息保存到上下文中供后续处理使用
			xQid := 10050
			expireAt := 4891334400000 // 2125-01-01 00:00:00（毫秒）

			ctx.Values().Save("x-qid", xQid, true)
			ctx.Values().Save("x-access-token", accessToken, true)
			ctx.Values().Save("x-token-expire-at", expireAt, true)

			ctx.Next()
			return
		}

		// 验证JWT令牌并提取用户信息
		claims, err := utils.ValidateJWT(accessToken)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"path":        ctx.Path(),
				"method":      ctx.Method(),
				"remote_addr": ctx.RemoteAddr(),
			}).Warn("JWT令牌验证失败")

			utils.UnauthorizedError(ctx, "")
			return
		}

		// 检查令牌是否过期
		now := time.Now().UnixMilli()
		expireAt := claims.ExpiresAt.UnixMilli()
		if expireAt < now {
			logrus.WithError(err).WithFields(logrus.Fields{
				"path":        ctx.Path(),
				"method":      ctx.Method(),
				"remote_addr": ctx.RemoteAddr(),
			}).Warn("JWT令牌验证已过期")

			utils.UnauthorizedError(ctx, "")
			return
		}

		// 把string类型qid转为int类型
		xQid, err := utils.DecodeQID(claims.Qid)
		if err != nil || xQid <= 0 {
			logrus.WithError(err).WithFields(logrus.Fields{
				"path":        ctx.Path(),
				"method":      ctx.Method(),
				"remote_addr": ctx.RemoteAddr(),
				"x_qid":       xQid,
			}).Warn("qid无效")

			utils.UnauthorizedError(ctx, "")
			return
		}

		// 查询是否在数据库中存在这个token
		ok, err := checkUserTokenCredential(xQid, accessToken)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"path":        ctx.Path(),
				"method":      ctx.Method(),
				"remote_addr": ctx.RemoteAddr(),
				"qid":         claims.Qid,
			}).Warn("CheckAuthorization checkUserTokenCredential 查询用户token失败")
			utils.InternalError(ctx, "")
			return
		}

		if ok == false {
			logrus.WithError(err).WithFields(logrus.Fields{
				"path":        ctx.Path(),
				"method":      ctx.Method(),
				"remote_addr": ctx.RemoteAddr(),
				"qid":         claims.Qid,
				"accessToken": accessToken,
			}).Warn("CheckAuthorization 用户token无效")
			utils.UnauthorizedError(ctx, "")
			return
		}

		// 将用户信息保存到上下文中供后续处理使用
		ctx.Values().Save("x-qid", xQid, true)
		ctx.Values().Save("x-access-token", accessToken, true)

		logrus.WithFields(logrus.Fields{
			"qid":  claims.Qid,
			"path": ctx.Path(),
		}).Debug("JWT令牌验证成功")

		ctx.Next()
	}
}

// CheckRequestSignature 处理请求签名
func CheckRequestSignature() iris.Handler {
	return func(ctx iris.Context) {
		signature := ctx.GetHeader("X-Signature")
		logrus.WithFields(logrus.Fields{
			"signature": signature,
			"path":      ctx.Path(),
		}).Debug("CheckRequestSignature Middleware")
		// TODO 判断请求签名是否正确
		//if signature == "" {
		//	logrus.WithFields(logrus.Fields{
		//		"missing_header": "X-Signature",
		//		"path":           ctx.Path(),
		//		"method":         ctx.Method(),
		//		"remote_addr":    ctx.RemoteAddr(),
		//	}).Warn("Missing required header")
		//
		//	utils.Error(ctx, utils.CodeUnauthorized, "Missing required header: X-Signature")
		//	return
		//}
		ctx.Next()
	}
}

// RequestHeaders 处理请求头
func RequestHeaders() iris.Handler {
	return func(ctx iris.Context) {
		deviceID := strings.TrimSpace(ctx.GetHeader("X-Device-Id"))
		if deviceID == "" {
			deviceID = "unknown-device-id"
		}
		ctx.Values().Save("x-device-id", deviceID, true)
		ctx.Next()
	}
}

// 是否是公开接口
func ispPublicAPI(requestPath string) bool {
	// 公共端点列表（无需认证）
	publicEndpoints := []string{
		"/api/app/config", // 应用配置
	}

	for _, endpoint := range publicEndpoints {
		if requestPath == endpoint {
			return true
		}
	}
	return false
}

// checkUserTokenCredential 检查数据库中token是否有效
func checkUserTokenCredential(qid int64, token string) (bool, error) {
	db := database.GetDB()
	if db == nil {
		return false, sql.ErrConnDone
	}

	query := `
		SELECT qid, crdt_type, crdt_key, crdt_value, created_at, expire_at, status, extra
		FROM user_credentials
		WHERE qid = $1 AND crdt_type = $2 AND status = $3 AND expire_at > $4 AND crdt_value = $5
	`

	now := time.Now().UnixMilli()
	credentialInfo := models.UserCredentialDBItem{}
	err := db.QueryRow(query, qid, models.CredentialTypeJWT, models.UserCredentialStatusNormal, now, token).Scan(&credentialInfo.Qid, &credentialInfo.CrdtType, &credentialInfo.CrdtKey, &credentialInfo.CrdtValue, &credentialInfo.CreatedAt, &credentialInfo.ExpireAt, &credentialInfo.Status, &credentialInfo.Extra)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			logrus.WithFields(logrus.Fields{
				"qid":   qid,
				"token": token,
			}).Warn("checkUserTokenCredential 未找到用户token")
			return false, nil // 密码不存在
		}
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":   qid,
			"token": token,
		}).Error("checkUserTokenCredential 查询用户token失败")
		return false, err
	}

	return true, nil
}
