package aliyun

import (
	"errors"
	"log"
	"qing-profiles/config"

	openapi "github.com/alibabacloud-go/darabonba-openapi/client"
	dypnsapi "github.com/alibabacloud-go/dypnsapi-20170525/client"
	util "github.com/alibabacloud-go/tea-utils/service"
	"github.com/alibabacloud-go/tea/tea"
)

var pnsClient *dypnsapi.Client

func init() {
	cfg := config.GetConfig()
	client, _err := createPnsClient(tea.String(cfg.Pns.AccessKeyId), tea.String(cfg.Pns.AccessKeySecret))
	if _err != nil {
		log.Fatalf("create aliyun client error=%v", _err)
		return
	}
	pnsClient = client
}

// 使用AK&SK初始化账号Client
func createPnsClient(accessKeyId *string, accessKeySecret *string) (_result *dypnsapi.Client, _err error) {
	config := &openapi.Config{}
	config.AccessKeyId = accessKeyId
	config.AccessKeySecret = accessKeySecret
	_result = &dypnsapi.Client{}
	_result, _err = dypnsapi.NewClient(config)
	return _result, _err
}

func GetMobile(token string) (string, error) {
	if pnsClient == nil {
		return "", errors.New("aliyun client is nil")
	}

	request := &dypnsapi.GetMobileRequest{}
	request.AccessToken = tea.String(token)
	response, _err := pnsClient.GetMobile(request)
	if _err != nil {
		return "", _err
	}

	code := response.Body.Code
	if !tea.BoolValue(util.EqualString(code, tea.String("OK"))) {
		log.Printf("GetMobile 错误信息= %v", tea.StringValue(response.Body.Message))
		return "", _err
	}

	phone := ""
	if response.Body != nil && response.Body.GetMobileResultDTO != nil && response.Body.GetMobileResultDTO.Mobile != nil {
		phone = tea.StringValue(response.Body.GetMobileResultDTO.Mobile)
		return phone, nil
	}
	return "", errors.New("bad response")
}
