package utils

import (
	"errors"
	"github.com/sqids/sqids-go"
)

var qidGenerator11 *sqids.Sqids
var defaultGenerator16 *sqids.Sqids

func init() {
	qidGenerator11, _ = sqids.New(sqids.Options{
		Alphabet:  "891736245", // 这个就是盐，换了字符的顺序会导致盐的变化
		MinLength: 11,
	})
	defaultGenerator16, _ = sqids.New(sqids.Options{
		Alphabet:  "KY0Ft5yasH7kBiMw1ruLlXzd94RQmJfjhTnc3VSNxqG8UODgZb6EoAIW2vepPC", // 0-9a-zA-Z 这个就是盐，换了字符的顺序会导致盐的变化
		MinLength: 16,
	})
}

func EncodeQID(qid int64) (string, error) {
	return qidGenerator11.Encode([]uint64{uint64(qid)})
}

func DecodeQID(qid string) (int64, error) {
	result := qidGenerator11.Decode(qid)
	if len(result) > 0 {
		return int64(result[0]), nil
	}
	return 0, errors.New("invalid qid")
}

func EncodeMediaID(mediaId int64) (string, error) {
	return defaultGenerator16.Encode([]uint64{uint64(mediaId)})
}

func DecodeMediaID(mediaId string) (int64, error) {
	result := defaultGenerator16.Decode(mediaId)
	if len(result) > 0 {
		return int64(result[0]), nil
	}
	return 0, errors.New("invalid mediaId")
}
