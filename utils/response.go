package utils

import (
	"github.com/kataras/iris/v12"
)

// StandardResponse represents the standard API response format
type StandardResponse struct {
	Code  int         `json:"code"`
	Data  interface{} `json:"data"`
	Msg   string      `json:"msg"`
	Extra interface{} `json:"extra"`
}

// response.data.code 含义 1～999 代表需要特殊处理的错误 1000以上是普通错误，客户端无需特殊处理
const (
	CodeSuccess         = 0
	CodePwdInitRequired = 1 // 需要初始化密码

	CodeInvalidRequest       = 1000 // 请求无效
	CodeUnauthorized         = 1001 // 鉴权失败
	CodeInvalidRequestParams = 1002 // 参数校验失败
	CodeForbidden            = 1003 // 权限不足
	CodeNotFound             = 1004 // 资源未找到
	CodeInternalError        = 1005 // 服务器内部错误
	CodeInvalidSignature     = 1006 // 签名无效
	CodeInvalidHeaders       = 1007 // 请求头错误
)

const DefaultErrorMessage = "请求失败，请稍后重试"
const UnauthorizedErrorMessage = "请求失败，请重新登录"
const InvalidRequestErrorMessage = "请求错误"
const InvalidRequestParamsErrorMessage = "请求参数错误"

// ensureNotNil ensures that data and extra fields are never nil
// Returns empty-map if the input is nil, otherwise returns the original value
func ensureNotNil(value interface{}) interface{} {
	if value == nil {
		return map[string]interface{}{}
	}
	return value
}

// SuccessWithMsg sends a successful response with custom-message
func SuccessWithMsg(ctx iris.Context, data interface{}, msg string) {
	response := StandardResponse{
		Code:  CodeSuccess,
		Data:  ensureNotNil(data),
		Msg:   msg,
		Extra: ensureNotNil(nil),
	}
	_ = ctx.JSON(response)
}

// Error sends an error response
func Error(ctx iris.Context, code int, msg string) {
	if msg == "" {
		msg = DefaultErrorMessage
	}
	response := StandardResponse{
		Code:  code,
		Data:  ensureNotNil(nil),
		Msg:   msg,
		Extra: ensureNotNil(nil),
	}
	ctx.StatusCode(getHTTPStatusCode(code))
	_ = ctx.JSON(response)
}

// CustomWithData sends an error response with data
func CustomWithData(ctx iris.Context, code int, msg string, data interface{}) {
	response := StandardResponse{
		Code:  code,
		Data:  ensureNotNil(data),
		Msg:   msg,
		Extra: ensureNotNil(nil),
	}
	ctx.StatusCode(getHTTPStatusCode(code))
	_ = ctx.JSON(response)
}

// getHTTPStatusCode maps internal error codes to HTTP status codes
func getHTTPStatusCode(code int) int {
	switch code {
	case CodeSuccess, CodePwdInitRequired:
		return iris.StatusOK
	case CodeInvalidRequest, CodeInvalidRequestParams, CodeInvalidSignature, CodeInvalidHeaders:
		return iris.StatusBadRequest
	case CodeUnauthorized:
		return iris.StatusUnauthorized
	case CodeForbidden:
		return iris.StatusForbidden
	case CodeNotFound:
		return iris.StatusNotFound
	case CodeInternalError:
		return iris.StatusInternalServerError
	default:
		return iris.StatusBadRequest // 默认错误
	}
}

// InvalidRequestError sends a validation error response
func InvalidRequestError(ctx iris.Context, msg string) {
	if msg == "" {
		msg = InvalidRequestErrorMessage
	}
	Error(ctx, CodeInvalidRequest, msg)
}

// InvalidRequestParamsError sends a validation error response
func InvalidRequestParamsError(ctx iris.Context, msg string) {
	if msg == "" {
		msg = InvalidRequestParamsErrorMessage
	}
	Error(ctx, CodeInvalidRequestParams, msg)
}

// UnauthorizedError sends an unauthorized error response
func UnauthorizedError(ctx iris.Context, msg string) {
	if msg == "" {
		msg = UnauthorizedErrorMessage
	}
	Error(ctx, CodeUnauthorized, msg)
}

// InternalError sends an internal server error response
func InternalError(ctx iris.Context, msg string) {
	if msg == "" {
		msg = DefaultErrorMessage
	}
	Error(ctx, CodeInternalError, msg)
}
