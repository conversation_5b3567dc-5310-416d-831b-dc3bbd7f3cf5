package utils

import (
	"errors"
	"qing-profiles/config"
	"sync"
	"time"
)

// Snowflake ID生成器
// 64位ID结构：1位符号位(0) + 41位时间戳 + 10位机器ID + 12位序列号

const (
	// 时间戳位数
	timestampBits = 41
	// 机器ID位数
	machineIDBits = 10
	// 序列号位数
	sequenceBits = 12

	// 最大机器ID
	maxMachineID = -1 ^ (-1 << machineIDBits)
	// 最大序列号
	maxSequence = -1 ^ (-1 << sequenceBits)

	// 时间戳左移位数
	timestampShift = sequenceBits + machineIDBits
	// 机器ID左移位数
	machineIDShift = sequenceBits

	// 基准时间戳 (2025-01-01 00:00:00 UTC)
	epoch = 1735660800000
)

// SnowflakeGenerator Snowflake ID生成器
type SnowflakeGenerator struct {
	mutex     sync.Mutex
	machineID int64
	sequence  int64
	lastTime  int64
}

var (
	defaultGenerator *SnowflakeGenerator
	backupGenerator  *SnowflakeGenerator
)

func init() {
	cfg := config.GetConfig()

	defaultGenerator = &SnowflakeGenerator{
		machineID: cfg.Snowflake.NodeId,
		sequence:  0,
		lastTime:  0,
	}
	backupGenerator = &SnowflakeGenerator{
		machineID: cfg.Snowflake.BackupNodeId,
		sequence:  0,
		lastTime:  0,
	}
}

// GenerateSnowflakeID 生成 Snowflake ID 使用两个 generator 再用纳秒做保底，确保一定会生成一个id
func GenerateSnowflakeID() int64 {
	id, err := defaultGenerator.NextID()
	if err == nil {
		return id
	}

	id, err = backupGenerator.NextID()
	if err == nil {
		return id
	}

	return time.Now().UnixNano()
}

// NextID 生成下一个ID
func (s *SnowflakeGenerator) NextID() (int64, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	now := time.Now().UnixMilli()

	if now < s.lastTime {
		return 0, errors.New("clock moved backwards")
	}

	if now == s.lastTime {
		s.sequence = (s.sequence + 1) & maxSequence
		if s.sequence == 0 {
			// 序列号用完，等待下一毫秒
			for now <= s.lastTime {
				now = time.Now().UnixMilli()
			}
		}
	} else {
		s.sequence = 0
	}

	s.lastTime = now

	// 生成ID
	id := ((now - epoch) << timestampShift) |
		(s.machineID << machineIDShift) |
		s.sequence

	return id, nil
}

//// ParseSnowflakeID 解析Snowflake ID
//func ParseSnowflakeID(id int64) (timestamp int64, machineID int64, sequence int64) {
//	timestamp = (id >> timestampShift) + epoch
//	machineID = (id >> machineIDShift) & maxMachineID
//	sequence = id & maxSequence
//	return
//}
//
//// GetTimestampFromID 从ID中获取时间戳
//func GetTimestampFromID(id int64) int64 {
//	timestamp, _, _ := ParseSnowflakeID(id)
//	return timestamp
//}
