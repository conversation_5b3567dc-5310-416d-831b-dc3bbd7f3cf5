package utils

import (
	"embed"
	"encoding/json"
)

//go:embed dist/*
var staticFiles embed.FS

type RegionProvince struct {
	Code           string        `json:"code"`
	Name           string        `json:"name"`
	Cities         []*RegionCity `json:"cities"`
	Areas          []*RegionArea `json:"areas"`
	IsMunicipality bool          `json:"is_municipality"` // 直辖市
}

type RegionCity struct {
	Code         string        `json:"code"`
	Name         string        `json:"name"`
	ProvinceCode string        `json:"provinceCode"`
	Areas        []*RegionArea `json:"areas"`
}
type RegionArea struct {
	Code         string `json:"code"`
	Name         string `json:"name"`
	CityCode     string `json:"cityCode"`
	ProvinceCode string `json:"provinceCode"`
}

var allProvinces []*RegionProvince
var allCities []*RegionCity
var allAreas []*RegionArea

func init() {
	provinceData, _ := staticFiles.ReadFile("dist/provinces.json")
	_ = json.Unmarshal(provinceData, &allProvinces)

	cityData, _ := staticFiles.ReadFile("dist/cities.json")
	_ = json.Unmarshal(cityData, &allCities)

	areaData, _ := staticFiles.ReadFile("dist/areas.json")
	_ = json.Unmarshal(areaData, &allAreas)

	initAllRegions()
}

func initAllRegions() {
	if len(allProvinces) <= 0 || len(allCities) <= 0 || len(allAreas) <= 0 {
		return
	}

	// 1. 找出每个省级下面的所有地级
	for _, province := range allProvinces {
		for _, city := range allCities {
			if city.ProvinceCode == province.Code {
				province.Cities = append(province.Cities, city)
			}
		}
		// 添加地级-不选
		province.Cities = append(province.Cities, &RegionCity{
			Code:         "none",
			Name:         "不选",
			ProvinceCode: province.Code,
			Areas:        []*RegionArea{},
		})
	}

	// 2. 找出每个地级下面的所有县级
	for _, city := range allCities {
		for _, area := range allAreas {
			if area.CityCode == city.Code {
				city.Areas = append(city.Areas, area)
			}
		}
		// 添加县级-不选
		city.Areas = append(city.Areas, &RegionArea{
			Code:         "none",
			Name:         "不选",
			CityCode:     city.Code,
			ProvinceCode: city.ProvinceCode,
		})
	}

	// 3. 处理4个直辖市
	for _, province := range allProvinces {
		if province.Code == "11" || province.Code == "12" || province.Code == "31" || province.Code == "50" {
			province.IsMunicipality = true
			province.Areas = province.Cities[0].Areas
			province.Cities = make([]*RegionCity, 0)
		} else {
			province.IsMunicipality = false
			province.Areas = make([]*RegionArea, 0)
		}
	}

	// 添加省级-不选
	allProvinces = append(allProvinces, &RegionProvince{
		Code:           "none",
		Name:           "不选",
		Cities:         []*RegionCity{},
		Areas:          []*RegionArea{},
		IsMunicipality: false,
	})
}

func GetAllRegions() []*RegionProvince {
	return allProvinces
}
