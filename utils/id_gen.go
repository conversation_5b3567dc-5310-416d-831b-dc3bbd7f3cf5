package utils

import (
	"errors"

	gonanoid "github.com/matoous/go-nanoid/v2"
)

func GenerateQID() (string, error) {
	for i := 0; i < 10; i++ {
		id, err := gonanoid.Generate("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", 14)
		if err == nil {
			return "QID_" + id, nil
		}
	}
	return "", errors.New("QID generation failure")
}

// GeneratePendingReviewTaskID 创建审核任务id
func GeneratePendingReviewTaskID() int64 {
	return GenerateSnowflakeID()
}
func GenerateMediaID() int64 {
	return GenerateSnowflakeID()
}
